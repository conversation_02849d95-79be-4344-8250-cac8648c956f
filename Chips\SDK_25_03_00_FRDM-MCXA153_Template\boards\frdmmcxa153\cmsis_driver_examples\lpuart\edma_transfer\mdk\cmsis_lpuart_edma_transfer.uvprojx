<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">
  <SchemaVersion>2.1</SchemaVersion>
  <Header>### uVision Project, (C) Keil Software</Header>
  <Targets>
    <Target>
      <TargetName>cmsis_lpuart_edma_transfer debug</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>6180000::V6.18::ARMCLANG</pCCUsed>
      <uAC6>1</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>MCXA153VLH</Device>
          <Vendor>NXP</Vendor>
          <PackID>NXP.MCXA153_DFP.17.0.0</PackID>
          <PackURL>https://mcuxpresso.nxp.com/cmsis_pack/repo/</PackURL>
          <Cpu>CPUTYPE("Cortex-M33")</Cpu>
          <FlashUtilSpec/>
          <StartupFile/>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:MCXA153VLH$devices/MCXA153/fsl_device_registers.h</RegisterFile>
          <MemoryEnv/>
          <Cmp/>
          <Asm/>
          <Linker/>
          <OHString/>
          <InfinionOptionDll/>
          <SLE66CMisc/>
          <SLE66AMisc/>
          <SLE66LinkerMisc/>
          <SFDFile>$$Device:MCXA153VLH$devices/MCXA153/MCXA153.xml</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath/>
          <IncludePath/>
          <LibPath/>
          <RegisterFilePath/>
          <DBRegisterFilePath/>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>debug\</OutputDirectory>
          <OutputName>cmsis_lpuart_edma_transfer.out</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>0</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>0</BrowseInformation>
          <ListingPath>.\Listings\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name/>
            <UserProg2Name/>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name/>
            <UserProg2Name/>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>fromelf.exe --output "$Lcmsis_lpuart_edma_transfer.bin" --bincombined "#L"</UserProg1Name>
            <UserProg2Name>fromelf --m32combined -o "$<EMAIL>" !L</UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>1</SelectedForBatchBuild>
          <SVCSIdString/>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument/>
          <IncludeLibraryModules/>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName/>
          <SimDllArguments/>
          <SimDlgDll/>
          <SimDlgDllArguments/>
          <TargetDllName>SARMV8M.DLL</TargetDllName>
          <TargetDllArguments/>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM33</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4101</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2V8M.DLL</Flash2>
          <Flash3/>
          <Flash4/>
          <pFcarmOut/>
          <pFcarmGrp/>
          <pFcArmRoot/>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>0</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>0</AdsLszi>
            <AdsLtoi>0</AdsLtoi>
            <AdsLsun>0</AdsLsun>
            <AdsLven>0</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M33"</AdsCpuType>
            <RvctDeviceName/>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>1</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>0</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <nBranchProt>0</nBranchProt>
            <hadIRAM2>1</hadIRAM2>
            <hadIROM2>1</hadIROM2>
            <StupSel>16</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>4</RoSelD>
            <RwSelD>4</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>0</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>0</Im1Chk>
            <Im2Chk>1</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x10000000</StartAddress>
                <Size>0x20000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x3000000</StartAddress>
                <Size>0x2000</Size>
              </IROM>
              <XRAM>
                <Type>1</Type>
                <StartAddress>0x4000000</StartAddress>
                <Size>0x2000</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x3000000</StartAddress>
                <Size>0x2000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x13000000</StartAddress>
                <Size>0x2000</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x4000000</StartAddress>
                <Size>0x2000</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x14000000</StartAddress>
                <Size>0x2000</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x4002000</StartAddress>
                <Size>0x1000</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x10000000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x6000</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector/>
          </ArmAdsMisc>
          <Cads>
            <interw>0</interw>
            <Optim>2</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>3</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>0</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>3</v6Lang>
            <v6LangP>3</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls>-include ../mcux_config.h -fno-common  -fdata-sections  -fno-builtin  -mthumb</MiscControls>
              <Define>DEBUG, SDK_DEBUGCONSOLE=1, MCUX_META_BUILD, MCUXPRESSO_SDK, CPU_MCXA153VLH</Define>
              <Undefine/>
              <IncludePath>..;../../../../../../devices/MCXA153/drivers;../../../../../../CMSIS/Core/Include;../../../../../../CMSIS/Core/Include/m-profile;../../../../../../CMSIS/Driver/Include;../../../../../../devices/MCXA153;../../../../../../devices/MCXA153/periph;../../../../../../devices/MCXA153/cmsis_drivers;../../../../../../devices/MCXA153/utilities/debug_console_lite;../../../../../../devices/MCXA153/utilities/str;../../../../../../components/uart</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>0</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>2</ClangAsOpt>
            <VariousControls>
              <MiscControls/>
              <Define>CPU_LPC55S69JBD100_cm33_core0, DEBUG,__CC_ARM,KEIL, MCUXPRESSO_SDK, CPU_MCXA153VLH</Define>
              <Undefine/>
              <IncludePath>..;../../../../../../devices/MCXA153/drivers;../../../../../../CMSIS/Core/Include;../../../../../../CMSIS/Core/Include/m-profile;../../../../../../CMSIS/Driver/Include;../../../../../../devices/MCXA153;../../../../../../devices/MCXA153/periph;../../../../../../devices/MCXA153/cmsis_drivers;../../../../../../devices/MCXA153/utilities/debug_console_lite;../../../../../../devices/MCXA153/utilities/str;../../../../../../components/uart</IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>0</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x20040000</DataAddressRange>
            <pXoBase/>
            <ScatterFile>MCXA153_flash.scf</ScatterFile>
            <IncludeLibs/>
            <IncludeLibsPath/>
            <Misc>--remove --entry=Reset_Handler --info=sizes,totals,unused,veneers</Misc>
            <LinkerInputFile/>
            <DisabledWarnings>6314</DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>source</GroupName>
          <Files>
            <File>
              <FileName>cmsis_usart_edma_transfer.c</FileName>
              <FileType>1</FileType>
              <FilePath>../cmsis_usart_edma_transfer.c</FilePath>
            </File>
            <File>
              <FileName>RTE_Device.h</FileName>
              <FileType>5</FileType>
              <FilePath>../RTE_Device.h</FilePath>
            </File>
            <File>
              <FileName>mcux_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>../mcux_config.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>board</GroupName>
          <Files>
            <File>
              <FileName>board.h</FileName>
              <FileType>5</FileType>
              <FilePath>../board.h</FilePath>
            </File>
            <File>
              <FileName>board.c</FileName>
              <FileType>1</FileType>
              <FilePath>../board.c</FilePath>
            </File>
            <File>
              <FileName>clock_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>../clock_config.h</FilePath>
            </File>
            <File>
              <FileName>clock_config.c</FileName>
              <FileType>1</FileType>
              <FilePath>../clock_config.c</FilePath>
            </File>
            <File>
              <FileName>pin_mux.c</FileName>
              <FileType>1</FileType>
              <FilePath>../pin_mux.c</FilePath>
            </File>
            <File>
              <FileName>pin_mux.h</FileName>
              <FileType>5</FileType>
              <FilePath>../pin_mux.h</FilePath>
            </File>
            <File>
              <FileName>hardware_init.c</FileName>
              <FileType>1</FileType>
              <FilePath>../hardware_init.c</FilePath>
            </File>
            <File>
              <FileName>app.h</FileName>
              <FileType>5</FileType>
              <FilePath>../app.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>drivers</GroupName>
          <Files>
            <File>
              <FileName>fsl_clock.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_clock.c</FilePath>
            </File>
            <File>
              <FileName>fsl_clock.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_clock.h</FilePath>
            </File>
            <File>
              <FileName>fsl_edma_soc.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_edma_soc.c</FilePath>
            </File>
            <File>
              <FileName>fsl_edma_soc.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_edma_soc.h</FilePath>
            </File>
            <File>
              <FileName>fsl_inputmux_connections.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_inputmux_connections.h</FilePath>
            </File>
            <File>
              <FileName>fsl_reset.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_reset.c</FilePath>
            </File>
            <File>
              <FileName>fsl_reset.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_reset.h</FilePath>
            </File>
            <File>
              <FileName>fsl_common.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_common.h</FilePath>
            </File>
            <File>
              <FileName>fsl_common.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_common.c</FilePath>
            </File>
            <File>
              <FileName>fsl_common_arm.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_common_arm.c</FilePath>
            </File>
            <File>
              <FileName>fsl_common_arm.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_common_arm.h</FilePath>
            </File>
            <File>
              <FileName>fsl_edma.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_edma.h</FilePath>
            </File>
            <File>
              <FileName>fsl_edma_core.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_edma_core.h</FilePath>
            </File>
            <File>
              <FileName>fsl_edma.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_edma.c</FilePath>
            </File>
            <File>
              <FileName>fsl_gpio.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_gpio.h</FilePath>
            </File>
            <File>
              <FileName>fsl_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_gpio.c</FilePath>
            </File>
            <File>
              <FileName>fsl_lpuart_edma.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_lpuart_edma.h</FilePath>
            </File>
            <File>
              <FileName>fsl_lpuart_edma.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_lpuart_edma.c</FilePath>
            </File>
            <File>
              <FileName>fsl_lpuart.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_lpuart.h</FilePath>
            </File>
            <File>
              <FileName>fsl_lpuart.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_lpuart.c</FilePath>
            </File>
            <File>
              <FileName>fsl_spc.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_spc.h</FilePath>
            </File>
            <File>
              <FileName>fsl_spc.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_spc.c</FilePath>
            </File>
            <File>
              <FileName>fsl_port.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_port.h</FilePath>
            </File>
            <File>
              <FileName>fsl_lpuart_cmsis.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/cmsis_drivers/fsl_lpuart_cmsis.h</FilePath>
            </File>
            <File>
              <FileName>fsl_lpuart_cmsis.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../devices/MCXA153/cmsis_drivers/fsl_lpuart_cmsis.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CMSIS</GroupName>
          <Files>
            <File>
              <FileName>cmsis_armclang.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../CMSIS/Core/Include/cmsis_armclang.h</FilePath>
            </File>
            <File>
              <FileName>cmsis_compiler.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../CMSIS/Core/Include/cmsis_compiler.h</FilePath>
            </File>
            <File>
              <FileName>cmsis_version.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../CMSIS/Core/Include/cmsis_version.h</FilePath>
            </File>
            <File>
              <FileName>core_cm33.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../CMSIS/Core/Include/core_cm33.h</FilePath>
            </File>
            <File>
              <FileName>tz_context.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../CMSIS/Core/Include/tz_context.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CMSIS/m-profile</GroupName>
          <Files>
            <File>
              <FileName>cmsis_armclang_m.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../CMSIS/Core/Include/m-profile/cmsis_armclang_m.h</FilePath>
            </File>
            <File>
              <FileName>armv8m_mpu.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../CMSIS/Core/Include/m-profile/armv8m_mpu.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CMSIS_driver/Include</GroupName>
          <Files>
            <File>
              <FileName>Driver_USART.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../CMSIS/Driver/Include/Driver_USART.h</FilePath>
            </File>
            <File>
              <FileName>Driver_Common.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../CMSIS/Driver/Include/Driver_Common.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>device</GroupName>
          <Files>
            <File>
              <FileName>fsl_device_registers.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/fsl_device_registers.h</FilePath>
            </File>
            <File>
              <FileName>MCXA153.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/MCXA153.h</FilePath>
            </File>
            <File>
              <FileName>MCXA153_features.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/MCXA153_features.h</FilePath>
            </File>
            <File>
              <FileName>MCXA153_COMMON.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/MCXA153_COMMON.h</FilePath>
            </File>
            <File>
              <FileName>system_MCXA153.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../devices/MCXA153/system_MCXA153.c</FilePath>
            </File>
            <File>
              <FileName>system_MCXA153.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/system_MCXA153.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>device/periph</GroupName>
          <Files>
            <File>
              <FileName>PERI_ADC.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_ADC.h</FilePath>
            </File>
            <File>
              <FileName>PERI_AOI.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_AOI.h</FilePath>
            </File>
            <File>
              <FileName>PERI_CDOG.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_CDOG.h</FilePath>
            </File>
            <File>
              <FileName>PERI_CMC.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_CMC.h</FilePath>
            </File>
            <File>
              <FileName>PERI_CRC.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_CRC.h</FilePath>
            </File>
            <File>
              <FileName>PERI_CTIMER.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_CTIMER.h</FilePath>
            </File>
            <File>
              <FileName>PERI_DEBUGMAILBOX.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_DEBUGMAILBOX.h</FilePath>
            </File>
            <File>
              <FileName>PERI_DMA.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_DMA.h</FilePath>
            </File>
            <File>
              <FileName>PERI_EIM.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_EIM.h</FilePath>
            </File>
            <File>
              <FileName>PERI_EQDC.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_EQDC.h</FilePath>
            </File>
            <File>
              <FileName>PERI_ERM.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_ERM.h</FilePath>
            </File>
            <File>
              <FileName>PERI_FMC.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_FMC.h</FilePath>
            </File>
            <File>
              <FileName>PERI_FMU.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_FMU.h</FilePath>
            </File>
            <File>
              <FileName>PERI_FMUTEST.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_FMUTEST.h</FilePath>
            </File>
            <File>
              <FileName>PERI_FREQME.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_FREQME.h</FilePath>
            </File>
            <File>
              <FileName>PERI_GLIKEY.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_GLIKEY.h</FilePath>
            </File>
            <File>
              <FileName>PERI_GPIO.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_GPIO.h</FilePath>
            </File>
            <File>
              <FileName>PERI_I3C.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_I3C.h</FilePath>
            </File>
            <File>
              <FileName>PERI_INPUTMUX.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_INPUTMUX.h</FilePath>
            </File>
            <File>
              <FileName>PERI_LPCMP.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_LPCMP.h</FilePath>
            </File>
            <File>
              <FileName>PERI_LPI2C.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_LPI2C.h</FilePath>
            </File>
            <File>
              <FileName>PERI_LPSPI.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_LPSPI.h</FilePath>
            </File>
            <File>
              <FileName>PERI_LPTMR.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_LPTMR.h</FilePath>
            </File>
            <File>
              <FileName>PERI_LPUART.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_LPUART.h</FilePath>
            </File>
            <File>
              <FileName>PERI_MRCC.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_MRCC.h</FilePath>
            </File>
            <File>
              <FileName>PERI_OSTIMER.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_OSTIMER.h</FilePath>
            </File>
            <File>
              <FileName>PERI_PORT.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_PORT.h</FilePath>
            </File>
            <File>
              <FileName>PERI_PWM.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_PWM.h</FilePath>
            </File>
            <File>
              <FileName>PERI_SCG.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_SCG.h</FilePath>
            </File>
            <File>
              <FileName>PERI_SPC.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_SPC.h</FilePath>
            </File>
            <File>
              <FileName>PERI_SYSCON.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_SYSCON.h</FilePath>
            </File>
            <File>
              <FileName>PERI_TRDC.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_TRDC.h</FilePath>
            </File>
            <File>
              <FileName>PERI_USB.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_USB.h</FilePath>
            </File>
            <File>
              <FileName>PERI_UTICK.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_UTICK.h</FilePath>
            </File>
            <File>
              <FileName>PERI_VBAT.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_VBAT.h</FilePath>
            </File>
            <File>
              <FileName>PERI_WAKETIMER.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_WAKETIMER.h</FilePath>
            </File>
            <File>
              <FileName>PERI_WUU.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_WUU.h</FilePath>
            </File>
            <File>
              <FileName>PERI_WWDT.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_WWDT.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>startup/arm</GroupName>
          <Files>
            <File>
              <FileName>startup_MCXA153.S</FileName>
              <FileType>2</FileType>
              <FilePath>../../../../../../devices/MCXA153/arm/startup_MCXA153.S</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>utilities</GroupName>
          <Files>
            <File>
              <FileName>fsl_assert.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/utilities/debug_console_lite/fsl_assert.h</FilePath>
            </File>
            <File>
              <FileName>fsl_assert.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../devices/MCXA153/utilities/debug_console_lite/fsl_assert.c</FilePath>
            </File>
            <File>
              <FileName>fsl_memcpy.S</FileName>
              <FileType>2</FileType>
              <FilePath>../../../../../../devices/MCXA153/utilities/fsl_memcpy.S</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>utilities/str</GroupName>
          <Files>
            <File>
              <FileName>fsl_str.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/utilities/str/fsl_str.h</FilePath>
            </File>
            <File>
              <FileName>fsl_str.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../devices/MCXA153/utilities/str/fsl_str.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>utilities/debug_console_lite</GroupName>
          <Files>
            <File>
              <FileName>fsl_debug_console.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/utilities/debug_console_lite/fsl_debug_console.h</FilePath>
            </File>
            <File>
              <FileName>fsl_debug_console.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../devices/MCXA153/utilities/debug_console_lite/fsl_debug_console.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>component/uart</GroupName>
          <Files>
            <File>
              <FileName>fsl_adapter_uart.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../components/uart/fsl_adapter_uart.h</FilePath>
            </File>
            <File>
              <FileName>fsl_adapter_lpuart.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../components/uart/fsl_adapter_lpuart.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>doc</GroupName>
          <Files>
            <File>
              <FileName>readme.md</FileName>
              <FileType>5</FileType>
              <FilePath>../readme.md</FilePath>
            </File>
            <File>
              <FileName>example_board_readme.md</FileName>
              <FileType>5</FileType>
              <FilePath>../example_board_readme.md</FilePath>
            </File>
            <File>
              <FileName>examples_shared_readme.md</FileName>
              <FileType>5</FileType>
              <FilePath>../examples_shared_readme.md</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
    <Target>
      <TargetName>cmsis_lpuart_edma_transfer release</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>6130001::V6.13.1::.\ARMCLANG</pCCUsed>
      <uAC6>1</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>MCXA153VLH</Device>
          <Vendor>NXP</Vendor>
          <PackID>NXP.MCXA153_DFP.17.0.0</PackID>
          <PackURL>https://mcuxpresso.nxp.com/cmsis_pack/repo/</PackURL>
          <Cpu>CPUTYPE("Cortex-M33")</Cpu>
          <FlashUtilSpec/>
          <StartupFile/>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:MCXA153VLH$devices/MCXA153/fsl_device_registers.h</RegisterFile>
          <MemoryEnv/>
          <Cmp/>
          <Asm/>
          <Linker/>
          <OHString/>
          <InfinionOptionDll/>
          <SLE66CMisc/>
          <SLE66AMisc/>
          <SLE66LinkerMisc/>
          <SFDFile>$$Device:MCXA153VLH$devices/MCXA153/MCXA153.xml</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath/>
          <IncludePath/>
          <LibPath/>
          <RegisterFilePath/>
          <DBRegisterFilePath/>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>release\</OutputDirectory>
          <OutputName>cmsis_lpuart_edma_transfer.out</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>0</CreateHexFile>
          <DebugInformation>0</DebugInformation>
          <BrowseInformation>0</BrowseInformation>
          <ListingPath>.\Listings\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name/>
            <UserProg2Name/>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name/>
            <UserProg2Name/>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>fromelf.exe --output "$Lcmsis_lpuart_edma_transfer.bin" --bincombined "#L"</UserProg1Name>
            <UserProg2Name>fromelf --m32combined -o "$<EMAIL>" !L</UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>1</SelectedForBatchBuild>
          <SVCSIdString/>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument/>
          <IncludeLibraryModules/>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName/>
          <SimDllArguments/>
          <SimDlgDll/>
          <SimDlgDllArguments/>
          <TargetDllName>SARMV8M.DLL</TargetDllName>
          <TargetDllArguments/>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM33</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4101</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2V8M.DLL</Flash2>
          <Flash3/>
          <Flash4/>
          <pFcarmOut/>
          <pFcarmGrp/>
          <pFcArmRoot/>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>0</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>0</AdsLszi>
            <AdsLtoi>0</AdsLtoi>
            <AdsLsun>0</AdsLsun>
            <AdsLven>0</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M33"</AdsCpuType>
            <RvctDeviceName/>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>1</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>0</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <nBranchProt>0</nBranchProt>
            <hadIRAM2>1</hadIRAM2>
            <hadIROM2>1</hadIROM2>
            <StupSel>16</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>4</RoSelD>
            <RwSelD>4</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>0</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>0</Im1Chk>
            <Im2Chk>1</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x10000000</StartAddress>
                <Size>0x20000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x3000000</StartAddress>
                <Size>0x2000</Size>
              </IROM>
              <XRAM>
                <Type>1</Type>
                <StartAddress>0x4000000</StartAddress>
                <Size>0x2000</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x3000000</StartAddress>
                <Size>0x2000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x13000000</StartAddress>
                <Size>0x2000</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x4000000</StartAddress>
                <Size>0x2000</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x14000000</StartAddress>
                <Size>0x2000</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x4002000</StartAddress>
                <Size>0x1000</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x10000000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x6000</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector/>
          </ArmAdsMisc>
          <Cads>
            <interw>0</interw>
            <Optim>7</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>3</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>0</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>3</v6Lang>
            <v6LangP>3</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls>-include ../mcux_config.h -fno-common  -fdata-sections  -fno-builtin  -mthumb</MiscControls>
              <Define>NDEBUG, SDK_DEBUGCONSOLE=1, MCUX_META_BUILD, MCUXPRESSO_SDK, CPU_MCXA153VLH</Define>
              <Undefine/>
              <IncludePath>..;../../../../../../devices/MCXA153/drivers;../../../../../../CMSIS/Core/Include;../../../../../../CMSIS/Core/Include/m-profile;../../../../../../CMSIS/Driver/Include;../../../../../../devices/MCXA153;../../../../../../devices/MCXA153/periph;../../../../../../devices/MCXA153/cmsis_drivers;../../../../../../devices/MCXA153/utilities/debug_console_lite;../../../../../../devices/MCXA153/utilities/str;../../../../../../components/uart</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>0</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>2</ClangAsOpt>
            <VariousControls>
              <MiscControls/>
              <Define>CPU_LPC55S69JBD100_cm33_core0, NDEBUG,__CC_ARM,KEIL, MCUXPRESSO_SDK, CPU_MCXA153VLH</Define>
              <Undefine/>
              <IncludePath>..;../../../../../../devices/MCXA153/drivers;../../../../../../CMSIS/Core/Include;../../../../../../CMSIS/Core/Include/m-profile;../../../../../../CMSIS/Driver/Include;../../../../../../devices/MCXA153;../../../../../../devices/MCXA153/periph;../../../../../../devices/MCXA153/cmsis_drivers;../../../../../../devices/MCXA153/utilities/debug_console_lite;../../../../../../devices/MCXA153/utilities/str;../../../../../../components/uart</IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>0</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x20040000</DataAddressRange>
            <pXoBase/>
            <ScatterFile>MCXA153_flash.scf</ScatterFile>
            <IncludeLibs/>
            <IncludeLibsPath/>
            <Misc>--remove --entry=Reset_Handler --info=sizes,totals,unused,veneers</Misc>
            <LinkerInputFile/>
            <DisabledWarnings>6314</DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>source</GroupName>
          <Files>
            <File>
              <FileName>cmsis_usart_edma_transfer.c</FileName>
              <FileType>1</FileType>
              <FilePath>../cmsis_usart_edma_transfer.c</FilePath>
            </File>
            <File>
              <FileName>RTE_Device.h</FileName>
              <FileType>5</FileType>
              <FilePath>../RTE_Device.h</FilePath>
            </File>
            <File>
              <FileName>mcux_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>../mcux_config.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>board</GroupName>
          <Files>
            <File>
              <FileName>board.h</FileName>
              <FileType>5</FileType>
              <FilePath>../board.h</FilePath>
            </File>
            <File>
              <FileName>board.c</FileName>
              <FileType>1</FileType>
              <FilePath>../board.c</FilePath>
            </File>
            <File>
              <FileName>clock_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>../clock_config.h</FilePath>
            </File>
            <File>
              <FileName>clock_config.c</FileName>
              <FileType>1</FileType>
              <FilePath>../clock_config.c</FilePath>
            </File>
            <File>
              <FileName>pin_mux.c</FileName>
              <FileType>1</FileType>
              <FilePath>../pin_mux.c</FilePath>
            </File>
            <File>
              <FileName>pin_mux.h</FileName>
              <FileType>5</FileType>
              <FilePath>../pin_mux.h</FilePath>
            </File>
            <File>
              <FileName>hardware_init.c</FileName>
              <FileType>1</FileType>
              <FilePath>../hardware_init.c</FilePath>
            </File>
            <File>
              <FileName>app.h</FileName>
              <FileType>5</FileType>
              <FilePath>../app.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>drivers</GroupName>
          <Files>
            <File>
              <FileName>fsl_clock.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_clock.c</FilePath>
            </File>
            <File>
              <FileName>fsl_clock.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_clock.h</FilePath>
            </File>
            <File>
              <FileName>fsl_edma_soc.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_edma_soc.c</FilePath>
            </File>
            <File>
              <FileName>fsl_edma_soc.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_edma_soc.h</FilePath>
            </File>
            <File>
              <FileName>fsl_inputmux_connections.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_inputmux_connections.h</FilePath>
            </File>
            <File>
              <FileName>fsl_reset.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_reset.c</FilePath>
            </File>
            <File>
              <FileName>fsl_reset.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_reset.h</FilePath>
            </File>
            <File>
              <FileName>fsl_common.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_common.h</FilePath>
            </File>
            <File>
              <FileName>fsl_common.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_common.c</FilePath>
            </File>
            <File>
              <FileName>fsl_common_arm.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_common_arm.c</FilePath>
            </File>
            <File>
              <FileName>fsl_common_arm.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_common_arm.h</FilePath>
            </File>
            <File>
              <FileName>fsl_edma.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_edma.h</FilePath>
            </File>
            <File>
              <FileName>fsl_edma_core.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_edma_core.h</FilePath>
            </File>
            <File>
              <FileName>fsl_edma.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_edma.c</FilePath>
            </File>
            <File>
              <FileName>fsl_gpio.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_gpio.h</FilePath>
            </File>
            <File>
              <FileName>fsl_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_gpio.c</FilePath>
            </File>
            <File>
              <FileName>fsl_lpuart_edma.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_lpuart_edma.h</FilePath>
            </File>
            <File>
              <FileName>fsl_lpuart_edma.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_lpuart_edma.c</FilePath>
            </File>
            <File>
              <FileName>fsl_lpuart.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_lpuart.h</FilePath>
            </File>
            <File>
              <FileName>fsl_lpuart.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_lpuart.c</FilePath>
            </File>
            <File>
              <FileName>fsl_spc.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_spc.h</FilePath>
            </File>
            <File>
              <FileName>fsl_spc.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_spc.c</FilePath>
            </File>
            <File>
              <FileName>fsl_port.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/drivers/fsl_port.h</FilePath>
            </File>
            <File>
              <FileName>fsl_lpuart_cmsis.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/cmsis_drivers/fsl_lpuart_cmsis.h</FilePath>
            </File>
            <File>
              <FileName>fsl_lpuart_cmsis.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../devices/MCXA153/cmsis_drivers/fsl_lpuart_cmsis.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CMSIS</GroupName>
          <Files>
            <File>
              <FileName>cmsis_armclang.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../CMSIS/Core/Include/cmsis_armclang.h</FilePath>
            </File>
            <File>
              <FileName>cmsis_compiler.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../CMSIS/Core/Include/cmsis_compiler.h</FilePath>
            </File>
            <File>
              <FileName>cmsis_version.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../CMSIS/Core/Include/cmsis_version.h</FilePath>
            </File>
            <File>
              <FileName>core_cm33.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../CMSIS/Core/Include/core_cm33.h</FilePath>
            </File>
            <File>
              <FileName>tz_context.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../CMSIS/Core/Include/tz_context.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CMSIS/m-profile</GroupName>
          <Files>
            <File>
              <FileName>cmsis_armclang_m.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../CMSIS/Core/Include/m-profile/cmsis_armclang_m.h</FilePath>
            </File>
            <File>
              <FileName>armv8m_mpu.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../CMSIS/Core/Include/m-profile/armv8m_mpu.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CMSIS_driver/Include</GroupName>
          <Files>
            <File>
              <FileName>Driver_USART.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../CMSIS/Driver/Include/Driver_USART.h</FilePath>
            </File>
            <File>
              <FileName>Driver_Common.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../CMSIS/Driver/Include/Driver_Common.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>device</GroupName>
          <Files>
            <File>
              <FileName>fsl_device_registers.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/fsl_device_registers.h</FilePath>
            </File>
            <File>
              <FileName>MCXA153.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/MCXA153.h</FilePath>
            </File>
            <File>
              <FileName>MCXA153_features.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/MCXA153_features.h</FilePath>
            </File>
            <File>
              <FileName>MCXA153_COMMON.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/MCXA153_COMMON.h</FilePath>
            </File>
            <File>
              <FileName>system_MCXA153.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../devices/MCXA153/system_MCXA153.c</FilePath>
            </File>
            <File>
              <FileName>system_MCXA153.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/system_MCXA153.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>device/periph</GroupName>
          <Files>
            <File>
              <FileName>PERI_ADC.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_ADC.h</FilePath>
            </File>
            <File>
              <FileName>PERI_AOI.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_AOI.h</FilePath>
            </File>
            <File>
              <FileName>PERI_CDOG.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_CDOG.h</FilePath>
            </File>
            <File>
              <FileName>PERI_CMC.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_CMC.h</FilePath>
            </File>
            <File>
              <FileName>PERI_CRC.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_CRC.h</FilePath>
            </File>
            <File>
              <FileName>PERI_CTIMER.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_CTIMER.h</FilePath>
            </File>
            <File>
              <FileName>PERI_DEBUGMAILBOX.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_DEBUGMAILBOX.h</FilePath>
            </File>
            <File>
              <FileName>PERI_DMA.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_DMA.h</FilePath>
            </File>
            <File>
              <FileName>PERI_EIM.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_EIM.h</FilePath>
            </File>
            <File>
              <FileName>PERI_EQDC.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_EQDC.h</FilePath>
            </File>
            <File>
              <FileName>PERI_ERM.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_ERM.h</FilePath>
            </File>
            <File>
              <FileName>PERI_FMC.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_FMC.h</FilePath>
            </File>
            <File>
              <FileName>PERI_FMU.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_FMU.h</FilePath>
            </File>
            <File>
              <FileName>PERI_FMUTEST.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_FMUTEST.h</FilePath>
            </File>
            <File>
              <FileName>PERI_FREQME.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_FREQME.h</FilePath>
            </File>
            <File>
              <FileName>PERI_GLIKEY.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_GLIKEY.h</FilePath>
            </File>
            <File>
              <FileName>PERI_GPIO.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_GPIO.h</FilePath>
            </File>
            <File>
              <FileName>PERI_I3C.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_I3C.h</FilePath>
            </File>
            <File>
              <FileName>PERI_INPUTMUX.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_INPUTMUX.h</FilePath>
            </File>
            <File>
              <FileName>PERI_LPCMP.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_LPCMP.h</FilePath>
            </File>
            <File>
              <FileName>PERI_LPI2C.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_LPI2C.h</FilePath>
            </File>
            <File>
              <FileName>PERI_LPSPI.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_LPSPI.h</FilePath>
            </File>
            <File>
              <FileName>PERI_LPTMR.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_LPTMR.h</FilePath>
            </File>
            <File>
              <FileName>PERI_LPUART.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_LPUART.h</FilePath>
            </File>
            <File>
              <FileName>PERI_MRCC.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_MRCC.h</FilePath>
            </File>
            <File>
              <FileName>PERI_OSTIMER.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_OSTIMER.h</FilePath>
            </File>
            <File>
              <FileName>PERI_PORT.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_PORT.h</FilePath>
            </File>
            <File>
              <FileName>PERI_PWM.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_PWM.h</FilePath>
            </File>
            <File>
              <FileName>PERI_SCG.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_SCG.h</FilePath>
            </File>
            <File>
              <FileName>PERI_SPC.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_SPC.h</FilePath>
            </File>
            <File>
              <FileName>PERI_SYSCON.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_SYSCON.h</FilePath>
            </File>
            <File>
              <FileName>PERI_TRDC.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_TRDC.h</FilePath>
            </File>
            <File>
              <FileName>PERI_USB.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_USB.h</FilePath>
            </File>
            <File>
              <FileName>PERI_UTICK.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_UTICK.h</FilePath>
            </File>
            <File>
              <FileName>PERI_VBAT.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_VBAT.h</FilePath>
            </File>
            <File>
              <FileName>PERI_WAKETIMER.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_WAKETIMER.h</FilePath>
            </File>
            <File>
              <FileName>PERI_WUU.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_WUU.h</FilePath>
            </File>
            <File>
              <FileName>PERI_WWDT.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/periph/PERI_WWDT.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>startup/arm</GroupName>
          <Files>
            <File>
              <FileName>startup_MCXA153.S</FileName>
              <FileType>2</FileType>
              <FilePath>../../../../../../devices/MCXA153/arm/startup_MCXA153.S</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>utilities</GroupName>
          <Files>
            <File>
              <FileName>fsl_assert.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/utilities/debug_console_lite/fsl_assert.h</FilePath>
            </File>
            <File>
              <FileName>fsl_assert.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../devices/MCXA153/utilities/debug_console_lite/fsl_assert.c</FilePath>
            </File>
            <File>
              <FileName>fsl_memcpy.S</FileName>
              <FileType>2</FileType>
              <FilePath>../../../../../../devices/MCXA153/utilities/fsl_memcpy.S</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>utilities/str</GroupName>
          <Files>
            <File>
              <FileName>fsl_str.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/utilities/str/fsl_str.h</FilePath>
            </File>
            <File>
              <FileName>fsl_str.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../devices/MCXA153/utilities/str/fsl_str.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>utilities/debug_console_lite</GroupName>
          <Files>
            <File>
              <FileName>fsl_debug_console.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../devices/MCXA153/utilities/debug_console_lite/fsl_debug_console.h</FilePath>
            </File>
            <File>
              <FileName>fsl_debug_console.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../devices/MCXA153/utilities/debug_console_lite/fsl_debug_console.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>component/uart</GroupName>
          <Files>
            <File>
              <FileName>fsl_adapter_uart.h</FileName>
              <FileType>5</FileType>
              <FilePath>../../../../../../components/uart/fsl_adapter_uart.h</FilePath>
            </File>
            <File>
              <FileName>fsl_adapter_lpuart.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../../../../../components/uart/fsl_adapter_lpuart.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>doc</GroupName>
          <Files>
            <File>
              <FileName>readme.md</FileName>
              <FileType>5</FileType>
              <FilePath>../readme.md</FilePath>
            </File>
            <File>
              <FileName>example_board_readme.md</FileName>
              <FileType>5</FileType>
              <FilePath>../example_board_readme.md</FilePath>
            </File>
            <File>
              <FileName>examples_shared_readme.md</FileName>
              <FileType>5</FileType>
              <FilePath>../examples_shared_readme.md</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>
  <RTE>
    <apis/>
    <components/>
    <files/>
  </RTE>
</Project>
