<?xml version="1.0" encoding="UTF-8"?>
<project>
  <fileVersion>4</fileVersion>
  <configuration>
    <name>debug</name>
    <toolchain>
      <name>ARM</name>
    </toolchain>
    <debug>1</debug>
    <settings>
      <name>C-SPY</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>33</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCOverrideSlave</name>
          <state>0</state>
        </option>
        <option>
          <name>OCOverrideSlavePath</name>
          <state/>
        </option>
        <option>
          <name>C_32_64Device</name>
          <state>1</state>
        </option>
        <option>
          <name>AuthEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>AuthSdmSelection</name>
          <state>1</state>
        </option>
        <option>
          <name>AuthSdmManifest</name>
          <state/>
        </option>
        <option>
          <name>AuthSdmExplicitLib</name>
          <state/>
        </option>
        <option>
          <name>AuthEnforce</name>
          <state>0</state>
        </option>
        <option>
          <name>CInput</name>
          <state>1</state>
        </option>
        <option>
          <name>CEndian</name>
          <state>1</state>
        </option>
        <option>
          <name>CProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>OCVariant</name>
          <state>0</state>
        </option>
        <option>
          <name>MacOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>MacFile</name>
          <state/>
        </option>
        <option>
          <name>MemOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>MemFile</name>
          <state/>
        </option>
        <option>
          <name>RunToEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>RunToName</name>
          <state>main</state>
        </option>
        <option>
          <name>CExtraOptionsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CExtraOptions</name>
          <state/>
        </option>
        <option>
          <name>CFpuProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>OCDDFArgumentProducer</name>
          <state/>
        </option>
        <option>
          <name>OCDownloadSuppressDownload</name>
          <state>0</state>
        </option>
        <option>
          <name>OCDownloadVerifyAll</name>
          <state>1</state>
        </option>
        <option>
          <name>OCProductVersion</name>
          <state>6.50.6.4952</state>
        </option>
        <option>
          <name>OCDynDriverList</name>
          <state>CMSISDAP_ID</state>
        </option>
        <option>
          <name>OCLastSavedByProductVersion</name>
          <state>9.60.1.2936</state>
        </option>
        <option>
          <name>UseFlashLoader</name>
          <state>1</state>
        </option>
        <option>
          <name>CLowLevel</name>
          <state>1</state>
        </option>
        <option>
          <name>OCBE8Slave</name>
          <state>1</state>
        </option>
        <option>
          <name>MacFile2</name>
          <state/>
        </option>
        <option>
          <name>CDevice</name>
          <state>1</state>
        </option>
        <option>
          <name>FlashLoadersV3</name>
          <state/>
        </option>
        <option>
          <name>OCImagesSuppressCheck1</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesPath1</name>
          <state/>
        </option>
        <option>
          <name>OCImagesSuppressCheck2</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesPath2</name>
          <state/>
        </option>
        <option>
          <name>OCImagesSuppressCheck3</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesPath3</name>
          <state/>
        </option>
        <option>
          <name>OverrideDefFlashBoard</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesOffset1</name>
          <state/>
        </option>
        <option>
          <name>OCImagesOffset2</name>
          <state/>
        </option>
        <option>
          <name>OCImagesOffset3</name>
          <state/>
        </option>
        <option>
          <name>OCImagesUse1</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesUse2</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesUse3</name>
          <state>0</state>
        </option>
        <option>
          <name>OCDeviceConfigMacroFile</name>
          <state>1</state>
        </option>
        <option>
          <name>OCDebuggerExtraOption</name>
          <state>1</state>
        </option>
        <option>
          <name>OCAllMTBOptions</name>
          <state>1</state>
        </option>
        <option>
          <name>OCMulticoreNrOfCores</name>
          <state>1</state>
        </option>
        <option>
          <name>OCMulticoreWorkspace</name>
          <state/>
        </option>
        <option>
          <name>OCMulticoreSlaveProject</name>
          <state/>
        </option>
        <option>
          <name>OCMulticoreSlaveConfiguration</name>
          <state/>
        </option>
        <option>
          <name>OCDownloadExtraImage</name>
          <state>1</state>
        </option>
        <option>
          <name>OCAttachSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>MassEraseBeforeFlashing</name>
          <state>0</state>
        </option>
        <option>
          <name>OCMulticoreNrOfCoresSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>OCMulticoreAMPConfigType</name>
          <state>0</state>
        </option>
        <option>
          <name>OCMulticoreSessionFile</name>
          <state/>
        </option>
        <option>
          <name>OCTpiuBaseOption</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>ARMSIM_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>1</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCSimDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>OCSimEnablePSP</name>
          <state>0</state>
        </option>
        <option>
          <name>OCSimPspOverrideConfig</name>
          <state>0</state>
        </option>
        <option>
          <name>OCSimPspConfigFile</name>
          <state/>
        </option>
      </data>
    </settings>
    <settings>
      <name>CADI_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>CCadiMemory</name>
          <state>1</state>
        </option>
        <option>
          <name>Fast Model</name>
          <state/>
        </option>
        <option>
          <name>CCADILogFileCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CCADILogFileEditB</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>CMSISDAP_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>4</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>CatchSFERR</name>
          <state>1</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>OCIarProbeScriptFile</name>
          <state>1</state>
        </option>
        <option>
          <name>CMSISDAPResetList</name>
          <version>1</version>
          <state>2</state>
        </option>
        <option>
          <name>CMSISDAPHWResetDuration</name>
          <state>300</state>
        </option>
        <option>
          <name>CMSISDAPHWResetDelay</name>
          <state>200</state>
        </option>
        <option>
          <name>CMSISDAPDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CMSISDAPInterfaceRadio</name>
          <state>1</state>
        </option>
        <option>
          <name>CMSISDAPInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiTargetEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPJtagSpeedList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPRestoreBreakpointsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPUpdateBreakpointsEdit</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>RDICatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchUndef</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchData</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchPrefetch</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchMMERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchNOCPERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchCHKERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchSTATERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchBUSERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchINTERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchHARDERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiCPUEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiCPUNumber</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeCfgOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeConfig</name>
          <state/>
        </option>
        <option>
          <name>CMSISDAPProbeConfigRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPSelectedCPUBehaviour</name>
          <state/>
        </option>
        <option>
          <name>ICpuName</name>
          <state/>
        </option>
        <option>
          <name>OCJetEmuParams</name>
          <state>1</state>
        </option>
        <option>
          <name>CCCMSISDAPUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCCMSISDAPUsbSerialNoSelect</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>E2_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>E2PowerFromProbe</name>
          <state>1</state>
        </option>
        <option>
          <name>CE2UsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CE2IdCodeEditB</name>
          <state>0xFFFF'FFFF'FFFF'FFFF'FFFF'FFFF'FFFF'FFFF</state>
        </option>
        <option>
          <name>CE2LogFileCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CE2LogFileEditB</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>GDBSERVER_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>TCPIP</name>
          <state>aaa.bbb.ccc.ddd</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCJTagBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJTagDoUpdateBreakpoints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJTagUpdateBreakpoints</name>
          <state>_call_main</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>GPLINK_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>IJET_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>10</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>CatchSFERR</name>
          <state>1</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>OCIarProbeScriptFile</name>
          <state>1</state>
        </option>
        <option>
          <name>IjetResetList</name>
          <version>1</version>
          <state>10</state>
        </option>
        <option>
          <name>IjetHWResetDuration</name>
          <state>300</state>
        </option>
        <option>
          <name>IjetHWResetDelay</name>
          <state>200</state>
        </option>
        <option>
          <name>IjetPowerFromProbe</name>
          <state>1</state>
        </option>
        <option>
          <name>IjetPowerRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>IjetInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiTargetEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetScanChainNonARMDevices</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetIRLength</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetJtagSpeedList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>IjetProtocolRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetSwoPin</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetCpuClockEdit</name>
          <state>72.0</state>
        </option>
        <option>
          <name>IjetSwoPrescalerList</name>
          <version>1</version>
          <state>0</state>
        </option>
        <option>
          <name>IjetBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetRestoreBreakpointsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetUpdateBreakpointsEdit</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>RDICatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchUndef</name>
          <state>1</state>
        </option>
        <option>
          <name>RDICatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchData</name>
          <state>1</state>
        </option>
        <option>
          <name>RDICatchPrefetch</name>
          <state>1</state>
        </option>
        <option>
          <name>RDICatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchMMERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchNOCPERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchCHKERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchSTATERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchBUSERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchINTERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchHARDERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeCfgOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeConfig</name>
          <state/>
        </option>
        <option>
          <name>IjetProbeConfigRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiCPUEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiCPUNumber</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetSelectedCPUBehaviour</name>
          <state>0</state>
        </option>
        <option>
          <name>ICpuName</name>
          <state/>
        </option>
        <option>
          <name>OCJetEmuParams</name>
          <state>1</state>
        </option>
        <option>
          <name>IjetPreferETB</name>
          <state>1</state>
        </option>
        <option>
          <name>IjetTraceSettingsList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>IjetTraceSizeList</name>
          <version>0</version>
          <state>4</state>
        </option>
        <option>
          <name>FlashBoardPathSlave</name>
          <state>0</state>
        </option>
        <option>
          <name>CCIjetUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCIjetUsbSerialNoSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARReset</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREREL1NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREREL1S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREREL2NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREREL3S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREEL1NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARREL1NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREEL1S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARREL1S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREEL2NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARREL2NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREEL3S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARREL3S</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetHWResetTimingOverride</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>JLINK_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>16</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>CCCatchSFERR</name>
          <state>0</state>
        </option>
        <option>
          <name>JLinkSpeed</name>
          <state>32</state>
        </option>
        <option>
          <name>CCJLinkDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCJLinkHWResetDelay</name>
          <state>0</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>JLinkInitialSpeed</name>
          <state>32</state>
        </option>
        <option>
          <name>CCDoJlinkMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>CCScanChainNonARMDevices</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkIRLength</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkCommRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkTCPIP</name>
          <state>aaa.bbb.ccc.ddd</state>
        </option>
        <option>
          <name>CCJLinkSpeedRadioV2</name>
          <state>0</state>
        </option>
        <option>
          <name>CCUSBDevice</name>
          <version>1</version>
          <state>1</state>
        </option>
        <option>
          <name>CCRDICatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchUndef</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchData</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchPrefetch</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkDoUpdateBreakpoints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkUpdateBreakpoints</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>CCJLinkInterfaceRadio</name>
          <state>1</state>
        </option>
        <option>
          <name>CCJLinkResetList</name>
          <version>6</version>
          <state>5</state>
        </option>
        <option>
          <name>CCJLinkInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchMMERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchNOCPERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchCHRERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchSTATERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchBUSERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchINTERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchHARDERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>OCJLinkScriptFile</name>
          <state>1</state>
        </option>
        <option>
          <name>CCJLinkUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCTcpIpAlt</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkTcpIpSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCCpuClockEdit</name>
          <state>72.0</state>
        </option>
        <option>
          <name>CCSwoClockAuto</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSwoClockEdit</name>
          <state>2000</state>
        </option>
        <option>
          <name>OCJLinkTraceSource</name>
          <state>0</state>
        </option>
        <option>
          <name>OCJLinkTraceSourceDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>OCJLinkDeviceName</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>LMIFTDI_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>3</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>LmiftdiSpeed</name>
          <state>500</state>
        </option>
        <option>
          <name>CCLmiftdiDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLmiftdiLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCLmiFtdiInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLmiFtdiInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLmiftdiUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCLmiftdiUsbSerialNoSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLmiftdiResetList</name>
          <version>0</version>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>NULINK_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>PEMICRO_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>3</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>CCJPEMicroShowSettings</name>
          <state>0</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>STLINK_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>8</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>CCSTLinkInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkResetList</name>
          <version>3</version>
          <state>0</state>
        </option>
        <option>
          <name>CCCpuClockEdit</name>
          <state>72.0</state>
        </option>
        <option>
          <name>CCSwoClockAuto</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSwoClockEdit</name>
          <state>2000</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCSTLinkDoUpdateBreakpoints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkUpdateBreakpoints</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>CCSTLinkCatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchMMERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchNOCPERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchCHRERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchSTATERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchBUSERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchINTERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchSFERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchHARDERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCSTLinkUsbSerialNoSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkJtagSpeedList</name>
          <version>2</version>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkDAPNumber</name>
          <state/>
        </option>
        <option>
          <name>CCSTLinkDebugAccessPortRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkUseServerSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkProbeList</name>
          <version>2</version>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkTargetVccEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>CCSTLinkTargetVoltage</name>
          <state>###Uninitialized###</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>THIRDPARTY_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>CThirdPartyDriverDll</name>
          <state>###Uninitialized###</state>
        </option>
        <option>
          <name>CThirdPartyLogFileCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CThirdPartyLogFileEditB</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>TIFET_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>1</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>CCMSPFetResetList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetTargetVccTypeDefault</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetTargetVoltage</name>
          <state>###Uninitialized###</state>
        </option>
        <option>
          <name>CCMSPFetVCCDefault</name>
          <state>1</state>
        </option>
        <option>
          <name>CCMSPFetTargetSettlingtime</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetRadioJtagSpeedType</name>
          <state>1</state>
        </option>
        <option>
          <name>CCMSPFetConnection</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetUsbComPort</name>
          <state>Automatic</state>
        </option>
        <option>
          <name>CCMSPFetAllowAccessToBSL</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCMSPFetRadioEraseFlash</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>XDS100_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>9</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>TIPackageOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>TIPackage</name>
          <state/>
        </option>
        <option>
          <name>BoardFile</name>
          <state/>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCXds100BreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100DoUpdateBreakpoints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100UpdateBreakpoints</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>CCXds100CatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchUndef</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchData</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchPrefetch</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchMMERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchNOCPERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchCHRERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchSTATERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchBUSERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchINTERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchSFERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchHARDERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CpuClockEdit</name>
          <state/>
        </option>
        <option>
          <name>CCXds100SwoClockAuto</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100SwoClockEdit</name>
          <state>1000</state>
        </option>
        <option>
          <name>CCXds100HWResetDelay</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100ResetList</name>
          <version>1</version>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100UsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCXds100UsbSerialNoSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100JtagSpeedList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100InterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100InterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100ProbeList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100SWOPortRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100SWOPort</name>
          <state>1</state>
        </option>
        <option>
          <name>CCXDSTargetVccEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXDSTargetVoltage</name>
          <state>###Uninitialized###</state>
        </option>
        <option>
          <name>OCXDSDigitalStatesConfigFile</name>
          <state>1</state>
        </option>
        <option>
          <name>OCSelectedCoreName</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <debuggerPlugins>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\Azure\AzureArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\CMX\CmxArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\CMX\CmxTinyArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\embOS\embOSPlugin.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\FreeRtos\FreeRtosArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\Mbed\MbedArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\Mbed\MbedArmPlugin2.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\OpenRTOS\OpenRTOSPlugin.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\SafeRTOS\SafeRTOSPlugin.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\SMX\smxAwareIarArm9a.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\ThreadX\ThreadXArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\uCOS-II\uCOS-II-286-KA-CSpy.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\uCOS-II\uCOS-II-KA-CSpy.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\uCOS-III\uCOS-III-KA-CSpy.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$EW_DIR$\common\plugins\Orti\Orti.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$EW_DIR$\common\plugins\TargetAccessServer\TargetAccessServer.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$EW_DIR$\common\plugins\uCProbe\uCProbePlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
    </debuggerPlugins>
  </configuration>
  <configuration>
    <name>release</name>
    <toolchain>
      <name>ARM</name>
    </toolchain>
    <debug>0</debug>
    <settings>
      <name>C-SPY</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>33</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCOverrideSlave</name>
          <state>0</state>
        </option>
        <option>
          <name>OCOverrideSlavePath</name>
          <state/>
        </option>
        <option>
          <name>C_32_64Device</name>
          <state>1</state>
        </option>
        <option>
          <name>AuthEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>AuthSdmSelection</name>
          <state>1</state>
        </option>
        <option>
          <name>AuthSdmManifest</name>
          <state/>
        </option>
        <option>
          <name>AuthSdmExplicitLib</name>
          <state/>
        </option>
        <option>
          <name>AuthEnforce</name>
          <state>0</state>
        </option>
        <option>
          <name>CInput</name>
          <state>1</state>
        </option>
        <option>
          <name>CEndian</name>
          <state>1</state>
        </option>
        <option>
          <name>CProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>OCVariant</name>
          <state>0</state>
        </option>
        <option>
          <name>MacOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>MacFile</name>
          <state/>
        </option>
        <option>
          <name>MemOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>MemFile</name>
          <state/>
        </option>
        <option>
          <name>RunToEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>RunToName</name>
          <state>main</state>
        </option>
        <option>
          <name>CExtraOptionsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CExtraOptions</name>
          <state/>
        </option>
        <option>
          <name>CFpuProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>OCDDFArgumentProducer</name>
          <state/>
        </option>
        <option>
          <name>OCDownloadSuppressDownload</name>
          <state>0</state>
        </option>
        <option>
          <name>OCDownloadVerifyAll</name>
          <state>1</state>
        </option>
        <option>
          <name>OCProductVersion</name>
          <state>6.50.6.4952</state>
        </option>
        <option>
          <name>OCDynDriverList</name>
          <state>CMSISDAP_ID</state>
        </option>
        <option>
          <name>OCLastSavedByProductVersion</name>
          <state>9.40.1.63870</state>
        </option>
        <option>
          <name>UseFlashLoader</name>
          <state>1</state>
        </option>
        <option>
          <name>CLowLevel</name>
          <state>1</state>
        </option>
        <option>
          <name>OCBE8Slave</name>
          <state>1</state>
        </option>
        <option>
          <name>MacFile2</name>
          <state/>
        </option>
        <option>
          <name>CDevice</name>
          <state>1</state>
        </option>
        <option>
          <name>FlashLoadersV3</name>
          <state/>
        </option>
        <option>
          <name>OCImagesSuppressCheck1</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesPath1</name>
          <state/>
        </option>
        <option>
          <name>OCImagesSuppressCheck2</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesPath2</name>
          <state/>
        </option>
        <option>
          <name>OCImagesSuppressCheck3</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesPath3</name>
          <state/>
        </option>
        <option>
          <name>OverrideDefFlashBoard</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesOffset1</name>
          <state/>
        </option>
        <option>
          <name>OCImagesOffset2</name>
          <state/>
        </option>
        <option>
          <name>OCImagesOffset3</name>
          <state/>
        </option>
        <option>
          <name>OCImagesUse1</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesUse2</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesUse3</name>
          <state>0</state>
        </option>
        <option>
          <name>OCDeviceConfigMacroFile</name>
          <state>1</state>
        </option>
        <option>
          <name>OCDebuggerExtraOption</name>
          <state>1</state>
        </option>
        <option>
          <name>OCAllMTBOptions</name>
          <state>1</state>
        </option>
        <option>
          <name>OCMulticoreNrOfCores</name>
          <state>1</state>
        </option>
        <option>
          <name>OCMulticoreWorkspace</name>
          <state/>
        </option>
        <option>
          <name>OCMulticoreSlaveProject</name>
          <state/>
        </option>
        <option>
          <name>OCMulticoreSlaveConfiguration</name>
          <state/>
        </option>
        <option>
          <name>OCDownloadExtraImage</name>
          <state>1</state>
        </option>
        <option>
          <name>OCAttachSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>MassEraseBeforeFlashing</name>
          <state>0</state>
        </option>
        <option>
          <name>OCMulticoreNrOfCoresSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>OCMulticoreAMPConfigType</name>
          <state>0</state>
        </option>
        <option>
          <name>OCMulticoreSessionFile</name>
          <state/>
        </option>
        <option>
          <name>OCTpiuBaseOption</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>ARMSIM_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>1</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCSimDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>OCSimEnablePSP</name>
          <state>0</state>
        </option>
        <option>
          <name>OCSimPspOverrideConfig</name>
          <state>0</state>
        </option>
        <option>
          <name>OCSimPspConfigFile</name>
          <state/>
        </option>
      </data>
    </settings>
    <settings>
      <name>CADI_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>CCadiMemory</name>
          <state>1</state>
        </option>
        <option>
          <name>Fast Model</name>
          <state/>
        </option>
        <option>
          <name>CCADILogFileCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CCADILogFileEditB</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>CMSISDAP_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>4</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>CatchSFERR</name>
          <state>1</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>OCIarProbeScriptFile</name>
          <state>1</state>
        </option>
        <option>
          <name>CMSISDAPResetList</name>
          <version>1</version>
          <state>2</state>
        </option>
        <option>
          <name>CMSISDAPHWResetDuration</name>
          <state>300</state>
        </option>
        <option>
          <name>CMSISDAPHWResetDelay</name>
          <state>200</state>
        </option>
        <option>
          <name>CMSISDAPDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CMSISDAPInterfaceRadio</name>
          <state>1</state>
        </option>
        <option>
          <name>CMSISDAPInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiTargetEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPJtagSpeedList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPRestoreBreakpointsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPUpdateBreakpointsEdit</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>RDICatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchUndef</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchData</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchPrefetch</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchMMERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchNOCPERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchCHKERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchSTATERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchBUSERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchINTERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchHARDERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiCPUEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiCPUNumber</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeCfgOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeConfig</name>
          <state/>
        </option>
        <option>
          <name>CMSISDAPProbeConfigRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPSelectedCPUBehaviour</name>
          <state/>
        </option>
        <option>
          <name>ICpuName</name>
          <state/>
        </option>
        <option>
          <name>OCJetEmuParams</name>
          <state>1</state>
        </option>
        <option>
          <name>CCCMSISDAPUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCCMSISDAPUsbSerialNoSelect</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>E2_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>E2PowerFromProbe</name>
          <state>1</state>
        </option>
        <option>
          <name>CE2UsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CE2IdCodeEditB</name>
          <state>0xFFFF'FFFF'FFFF'FFFF'FFFF'FFFF'FFFF'FFFF</state>
        </option>
        <option>
          <name>CE2LogFileCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CE2LogFileEditB</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>GDBSERVER_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>TCPIP</name>
          <state>aaa.bbb.ccc.ddd</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCJTagBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJTagDoUpdateBreakpoints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJTagUpdateBreakpoints</name>
          <state>_call_main</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>GPLINK_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>IJET_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>10</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>CatchSFERR</name>
          <state>1</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>OCIarProbeScriptFile</name>
          <state>1</state>
        </option>
        <option>
          <name>IjetResetList</name>
          <version>1</version>
          <state>10</state>
        </option>
        <option>
          <name>IjetHWResetDuration</name>
          <state>300</state>
        </option>
        <option>
          <name>IjetHWResetDelay</name>
          <state>200</state>
        </option>
        <option>
          <name>IjetPowerFromProbe</name>
          <state>1</state>
        </option>
        <option>
          <name>IjetPowerRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>IjetInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiTargetEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetScanChainNonARMDevices</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetIRLength</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetJtagSpeedList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>IjetProtocolRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetSwoPin</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetCpuClockEdit</name>
          <state>72.0</state>
        </option>
        <option>
          <name>IjetSwoPrescalerList</name>
          <version>1</version>
          <state>0</state>
        </option>
        <option>
          <name>IjetBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetRestoreBreakpointsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetUpdateBreakpointsEdit</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>RDICatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchUndef</name>
          <state>1</state>
        </option>
        <option>
          <name>RDICatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchData</name>
          <state>1</state>
        </option>
        <option>
          <name>RDICatchPrefetch</name>
          <state>1</state>
        </option>
        <option>
          <name>RDICatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchMMERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchNOCPERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchCHKERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchSTATERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchBUSERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchINTERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchHARDERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeCfgOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeConfig</name>
          <state/>
        </option>
        <option>
          <name>IjetProbeConfigRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiCPUEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiCPUNumber</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetSelectedCPUBehaviour</name>
          <state>0</state>
        </option>
        <option>
          <name>ICpuName</name>
          <state/>
        </option>
        <option>
          <name>OCJetEmuParams</name>
          <state>1</state>
        </option>
        <option>
          <name>IjetPreferETB</name>
          <state>1</state>
        </option>
        <option>
          <name>IjetTraceSettingsList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>IjetTraceSizeList</name>
          <version>0</version>
          <state>4</state>
        </option>
        <option>
          <name>FlashBoardPathSlave</name>
          <state>0</state>
        </option>
        <option>
          <name>CCIjetUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCIjetUsbSerialNoSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARReset</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREREL1NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREREL1S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREREL2NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREREL3S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREEL1NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARREL1NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREEL1S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARREL1S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREEL2NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARREL2NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREEL3S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARREL3S</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetHWResetTimingOverride</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>JLINK_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>16</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>CCCatchSFERR</name>
          <state>0</state>
        </option>
        <option>
          <name>JLinkSpeed</name>
          <state>32</state>
        </option>
        <option>
          <name>CCJLinkDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCJLinkHWResetDelay</name>
          <state>0</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>JLinkInitialSpeed</name>
          <state>32</state>
        </option>
        <option>
          <name>CCDoJlinkMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>CCScanChainNonARMDevices</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkIRLength</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkCommRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkTCPIP</name>
          <state>aaa.bbb.ccc.ddd</state>
        </option>
        <option>
          <name>CCJLinkSpeedRadioV2</name>
          <state>0</state>
        </option>
        <option>
          <name>CCUSBDevice</name>
          <version>1</version>
          <state>1</state>
        </option>
        <option>
          <name>CCRDICatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchUndef</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchData</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchPrefetch</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkDoUpdateBreakpoints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkUpdateBreakpoints</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>CCJLinkInterfaceRadio</name>
          <state>1</state>
        </option>
        <option>
          <name>CCJLinkResetList</name>
          <version>6</version>
          <state>5</state>
        </option>
        <option>
          <name>CCJLinkInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchMMERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchNOCPERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchCHRERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchSTATERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchBUSERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchINTERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchHARDERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>OCJLinkScriptFile</name>
          <state>1</state>
        </option>
        <option>
          <name>CCJLinkUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCTcpIpAlt</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkTcpIpSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCCpuClockEdit</name>
          <state>72.0</state>
        </option>
        <option>
          <name>CCSwoClockAuto</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSwoClockEdit</name>
          <state>2000</state>
        </option>
        <option>
          <name>OCJLinkTraceSource</name>
          <state>0</state>
        </option>
        <option>
          <name>OCJLinkTraceSourceDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>OCJLinkDeviceName</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>LMIFTDI_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>3</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>LmiftdiSpeed</name>
          <state>500</state>
        </option>
        <option>
          <name>CCLmiftdiDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLmiftdiLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCLmiFtdiInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLmiFtdiInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLmiftdiUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCLmiftdiUsbSerialNoSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLmiftdiResetList</name>
          <version>0</version>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>NULINK_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>PEMICRO_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>3</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>CCJPEMicroShowSettings</name>
          <state>0</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>STLINK_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>8</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>CCSTLinkInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkResetList</name>
          <version>3</version>
          <state>0</state>
        </option>
        <option>
          <name>CCCpuClockEdit</name>
          <state>72.0</state>
        </option>
        <option>
          <name>CCSwoClockAuto</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSwoClockEdit</name>
          <state>2000</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCSTLinkDoUpdateBreakpoints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkUpdateBreakpoints</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>CCSTLinkCatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchMMERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchNOCPERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchCHRERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchSTATERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchBUSERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchINTERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchSFERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchHARDERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCSTLinkUsbSerialNoSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkJtagSpeedList</name>
          <version>2</version>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkDAPNumber</name>
          <state/>
        </option>
        <option>
          <name>CCSTLinkDebugAccessPortRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkUseServerSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkProbeList</name>
          <version>2</version>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkTargetVccEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>CCSTLinkTargetVoltage</name>
          <state>###Uninitialized###</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>THIRDPARTY_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>CThirdPartyDriverDll</name>
          <state>###Uninitialized###</state>
        </option>
        <option>
          <name>CThirdPartyLogFileCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CThirdPartyLogFileEditB</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>TIFET_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>1</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>CCMSPFetResetList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetTargetVccTypeDefault</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetTargetVoltage</name>
          <state>###Uninitialized###</state>
        </option>
        <option>
          <name>CCMSPFetVCCDefault</name>
          <state>1</state>
        </option>
        <option>
          <name>CCMSPFetTargetSettlingtime</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetRadioJtagSpeedType</name>
          <state>1</state>
        </option>
        <option>
          <name>CCMSPFetConnection</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetUsbComPort</name>
          <state>Automatic</state>
        </option>
        <option>
          <name>CCMSPFetAllowAccessToBSL</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCMSPFetRadioEraseFlash</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>XDS100_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>9</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>TIPackageOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>TIPackage</name>
          <state/>
        </option>
        <option>
          <name>BoardFile</name>
          <state/>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCXds100BreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100DoUpdateBreakpoints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100UpdateBreakpoints</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>CCXds100CatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchUndef</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchData</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchPrefetch</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchMMERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchNOCPERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchCHRERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchSTATERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchBUSERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchINTERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchSFERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchHARDERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CpuClockEdit</name>
          <state/>
        </option>
        <option>
          <name>CCXds100SwoClockAuto</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100SwoClockEdit</name>
          <state>1000</state>
        </option>
        <option>
          <name>CCXds100HWResetDelay</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100ResetList</name>
          <version>1</version>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100UsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCXds100UsbSerialNoSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100JtagSpeedList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100InterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100InterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100ProbeList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100SWOPortRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100SWOPort</name>
          <state>1</state>
        </option>
        <option>
          <name>CCXDSTargetVccEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXDSTargetVoltage</name>
          <state>###Uninitialized###</state>
        </option>
        <option>
          <name>OCXDSDigitalStatesConfigFile</name>
          <state>1</state>
        </option>
        <option>
          <name>OCSelectedCoreName</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <debuggerPlugins>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\Azure\AzureArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\CMX\CmxArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\CMX\CmxTinyArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\embOS\embOSPlugin.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\FreeRtos\FreeRtosArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\Mbed\MbedArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\Mbed\MbedArmPlugin2.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\OpenRTOS\OpenRTOSPlugin.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\SafeRTOS\SafeRTOSPlugin.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\SMX\smxAwareIarArm9a.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\ThreadX\ThreadXArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\uCOS-II\uCOS-II-286-KA-CSpy.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\uCOS-II\uCOS-II-KA-CSpy.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\uCOS-III\uCOS-III-KA-CSpy.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$EW_DIR$\common\plugins\Orti\Orti.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$EW_DIR$\common\plugins\TargetAccessServer\TargetAccessServer.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$EW_DIR$\common\plugins\uCProbe\uCProbePlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
    </debuggerPlugins>
  </configuration>
  <configuration>
    <name>int flash sramdata debug</name>
    <toolchain>
      <name>ARM</name>
    </toolchain>
    <debug>1</debug>
    <settings>
      <name>C-SPY</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>33</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCOverrideSlave</name>
          <state>0</state>
        </option>
        <option>
          <name>OCOverrideSlavePath</name>
          <state/>
        </option>
        <option>
          <name>C_32_64Device</name>
          <state>1</state>
        </option>
        <option>
          <name>AuthEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>AuthSdmSelection</name>
          <state>1</state>
        </option>
        <option>
          <name>AuthSdmManifest</name>
          <state/>
        </option>
        <option>
          <name>AuthSdmExplicitLib</name>
          <state/>
        </option>
        <option>
          <name>AuthEnforce</name>
          <state>0</state>
        </option>
        <option>
          <name>CInput</name>
          <state>1</state>
        </option>
        <option>
          <name>CEndian</name>
          <state>1</state>
        </option>
        <option>
          <name>CProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>OCVariant</name>
          <state>0</state>
        </option>
        <option>
          <name>MacOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>MacFile</name>
          <state/>
        </option>
        <option>
          <name>MemOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>MemFile</name>
          <state>$TOOLKIT_DIR$\CONFIG\debugger\Freescale\MK70FN1M0xxx12.ddf</state>
        </option>
        <option>
          <name>RunToEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>RunToName</name>
          <state>main</state>
        </option>
        <option>
          <name>CExtraOptionsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CExtraOptions</name>
          <state/>
        </option>
        <option>
          <name>CFpuProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>OCDDFArgumentProducer</name>
          <state/>
        </option>
        <option>
          <name>OCDownloadSuppressDownload</name>
          <state>0</state>
        </option>
        <option>
          <name>OCDownloadVerifyAll</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProductVersion</name>
          <state>5.50.0.51907</state>
        </option>
        <option>
          <name>OCDynDriverList</name>
          <state>JLINK_ID</state>
        </option>
        <option>
          <name>OCLastSavedByProductVersion</name>
          <state>6.40.2.53991</state>
        </option>
        <option>
          <name>UseFlashLoader</name>
          <state>1</state>
        </option>
        <option>
          <name>CLowLevel</name>
          <state>1</state>
        </option>
        <option>
          <name>OCBE8Slave</name>
          <state>1</state>
        </option>
        <option>
          <name>MacFile2</name>
          <state/>
        </option>
        <option>
          <name>CDevice</name>
          <state>1</state>
        </option>
        <option>
          <name>FlashLoadersV3</name>
          <state>$TOOLKIT_DIR$\config\flashloader\Freescale\FlashK70Fxxx128K.board</state>
        </option>
        <option>
          <name>OCImagesSuppressCheck1</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesPath1</name>
          <state/>
        </option>
        <option>
          <name>OCImagesSuppressCheck2</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesPath2</name>
          <state/>
        </option>
        <option>
          <name>OCImagesSuppressCheck3</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesPath3</name>
          <state/>
        </option>
        <option>
          <name>OverrideDefFlashBoard</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesOffset1</name>
          <state/>
        </option>
        <option>
          <name>OCImagesOffset2</name>
          <state/>
        </option>
        <option>
          <name>OCImagesOffset3</name>
          <state/>
        </option>
        <option>
          <name>OCImagesUse1</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesUse2</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesUse3</name>
          <state>0</state>
        </option>
        <option>
          <name>OCDeviceConfigMacroFile</name>
          <state>1</state>
        </option>
        <option>
          <name>OCDebuggerExtraOption</name>
          <state>1</state>
        </option>
        <option>
          <name>OCAllMTBOptions</name>
          <state>1</state>
        </option>
        <option>
          <name>OCMulticoreNrOfCores</name>
          <state>1</state>
        </option>
        <option>
          <name>OCMulticoreWorkspace</name>
          <state/>
        </option>
        <option>
          <name>OCMulticoreSlaveProject</name>
          <state/>
        </option>
        <option>
          <name>OCMulticoreSlaveConfiguration</name>
          <state/>
        </option>
        <option>
          <name>OCDownloadExtraImage</name>
          <state>1</state>
        </option>
        <option>
          <name>OCAttachSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>MassEraseBeforeFlashing</name>
          <state>0</state>
        </option>
        <option>
          <name>OCMulticoreNrOfCoresSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>OCMulticoreAMPConfigType</name>
          <state>0</state>
        </option>
        <option>
          <name>OCMulticoreSessionFile</name>
          <state/>
        </option>
        <option>
          <name>OCTpiuBaseOption</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>ARMSIM_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>1</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCSimDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>OCSimEnablePSP</name>
          <state>0</state>
        </option>
        <option>
          <name>OCSimPspOverrideConfig</name>
          <state>0</state>
        </option>
        <option>
          <name>OCSimPspConfigFile</name>
          <state/>
        </option>
      </data>
    </settings>
    <settings>
      <name>CADI_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>CCadiMemory</name>
          <state>1</state>
        </option>
        <option>
          <name>Fast Model</name>
          <state/>
        </option>
        <option>
          <name>CCADILogFileCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CCADILogFileEditB</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>CMSISDAP_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>4</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>CatchSFERR</name>
          <state>1</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>OCIarProbeScriptFile</name>
          <state>1</state>
        </option>
        <option>
          <name>CMSISDAPResetList</name>
          <version>1</version>
          <state>10</state>
        </option>
        <option>
          <name>CMSISDAPHWResetDuration</name>
          <state>300</state>
        </option>
        <option>
          <name>CMSISDAPHWResetDelay</name>
          <state>200</state>
        </option>
        <option>
          <name>CMSISDAPDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CMSISDAPInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiTargetEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPJtagSpeedList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPRestoreBreakpointsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPUpdateBreakpointsEdit</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>RDICatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchUndef</name>
          <state>1</state>
        </option>
        <option>
          <name>RDICatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchData</name>
          <state>1</state>
        </option>
        <option>
          <name>RDICatchPrefetch</name>
          <state>1</state>
        </option>
        <option>
          <name>RDICatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchMMERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchNOCPERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchCHKERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchSTATERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchBUSERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchINTERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchHARDERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiCPUEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiCPUNumber</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeCfgOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeConfig</name>
          <state/>
        </option>
        <option>
          <name>CMSISDAPProbeConfigRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPSelectedCPUBehaviour</name>
          <state>0</state>
        </option>
        <option>
          <name>ICpuName</name>
          <state/>
        </option>
        <option>
          <name>OCJetEmuParams</name>
          <state>1</state>
        </option>
        <option>
          <name>CCCMSISDAPUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCCMSISDAPUsbSerialNoSelect</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>E2_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>E2PowerFromProbe</name>
          <state>1</state>
        </option>
        <option>
          <name>CE2UsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CE2IdCodeEditB</name>
          <state>0xFFFF'FFFF'FFFF'FFFF'FFFF'FFFF'FFFF'FFFF</state>
        </option>
        <option>
          <name>CE2LogFileCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CE2LogFileEditB</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>GDBSERVER_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>TCPIP</name>
          <state>aaa.bbb.ccc.ddd</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCJTagBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJTagDoUpdateBreakpoints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJTagUpdateBreakpoints</name>
          <state>_call_main</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>GPLINK_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>IJET_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>10</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>CatchSFERR</name>
          <state>1</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>OCIarProbeScriptFile</name>
          <state>1</state>
        </option>
        <option>
          <name>IjetResetList</name>
          <version>1</version>
          <state>10</state>
        </option>
        <option>
          <name>IjetHWResetDuration</name>
          <state>300</state>
        </option>
        <option>
          <name>IjetHWResetDelay</name>
          <state>200</state>
        </option>
        <option>
          <name>IjetPowerFromProbe</name>
          <state>1</state>
        </option>
        <option>
          <name>IjetPowerRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>IjetInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiTargetEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetScanChainNonARMDevices</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetIRLength</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetJtagSpeedList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>IjetProtocolRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetSwoPin</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetCpuClockEdit</name>
          <state>72.0</state>
        </option>
        <option>
          <name>IjetSwoPrescalerList</name>
          <version>1</version>
          <state>0</state>
        </option>
        <option>
          <name>IjetBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetRestoreBreakpointsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetUpdateBreakpointsEdit</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>RDICatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchUndef</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchData</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchPrefetch</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchMMERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchNOCPERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchCHKERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchSTATERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchBUSERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchINTERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchHARDERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeCfgOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeConfig</name>
          <state/>
        </option>
        <option>
          <name>IjetProbeConfigRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiCPUEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiCPUNumber</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetSelectedCPUBehaviour</name>
          <state>0</state>
        </option>
        <option>
          <name>ICpuName</name>
          <state/>
        </option>
        <option>
          <name>OCJetEmuParams</name>
          <state>1</state>
        </option>
        <option>
          <name>IjetPreferETB</name>
          <state>1</state>
        </option>
        <option>
          <name>IjetTraceSettingsList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>IjetTraceSizeList</name>
          <version>0</version>
          <state>4</state>
        </option>
        <option>
          <name>FlashBoardPathSlave</name>
          <state>0</state>
        </option>
        <option>
          <name>CCIjetUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCIjetUsbSerialNoSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARReset</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREREL1NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREREL1S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREREL2NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREREL3S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREEL1NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARREL1NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREEL1S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARREL1S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREEL2NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARREL2NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREEL3S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARREL3S</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetHWResetTimingOverride</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>JLINK_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>16</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>CCCatchSFERR</name>
          <state>0</state>
        </option>
        <option>
          <name>JLinkSpeed</name>
          <state>100</state>
        </option>
        <option>
          <name>CCJLinkDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCJLinkHWResetDelay</name>
          <state>0</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>JLinkInitialSpeed</name>
          <state>32</state>
        </option>
        <option>
          <name>CCDoJlinkMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>CCScanChainNonARMDevices</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkIRLength</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkCommRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkTCPIP</name>
          <state/>
        </option>
        <option>
          <name>CCJLinkSpeedRadioV2</name>
          <state>0</state>
        </option>
        <option>
          <name>CCUSBDevice</name>
          <version>1</version>
          <state>1</state>
        </option>
        <option>
          <name>CCRDICatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchUndef</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchData</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchPrefetch</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkDoUpdateBreakpoints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkUpdateBreakpoints</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>CCJLinkInterfaceRadio</name>
          <state>1</state>
        </option>
        <option>
          <name>CCJLinkResetList</name>
          <version>6</version>
          <state>7</state>
        </option>
        <option>
          <name>CCJLinkInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchMMERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchNOCPERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchCHRERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchSTATERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchBUSERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchINTERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchHARDERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>OCJLinkScriptFile</name>
          <state>1</state>
        </option>
        <option>
          <name>CCJLinkUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCTcpIpAlt</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkTcpIpSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCCpuClockEdit</name>
          <state>72.0</state>
        </option>
        <option>
          <name>CCSwoClockAuto</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSwoClockEdit</name>
          <state>2000</state>
        </option>
        <option>
          <name>OCJLinkTraceSource</name>
          <state>0</state>
        </option>
        <option>
          <name>OCJLinkTraceSourceDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>OCJLinkDeviceName</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>LMIFTDI_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>3</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>LmiftdiSpeed</name>
          <state>500</state>
        </option>
        <option>
          <name>CCLmiftdiDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLmiftdiLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCLmiFtdiInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLmiFtdiInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLmiftdiUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCLmiftdiUsbSerialNoSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLmiftdiResetList</name>
          <version>0</version>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>NULINK_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>PEMICRO_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>3</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>CCJPEMicroShowSettings</name>
          <state>0</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>STLINK_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>8</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>CCSTLinkInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkResetList</name>
          <version>3</version>
          <state>0</state>
        </option>
        <option>
          <name>CCCpuClockEdit</name>
          <state>72.0</state>
        </option>
        <option>
          <name>CCSwoClockAuto</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSwoClockEdit</name>
          <state>2000</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCSTLinkDoUpdateBreakpoints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkUpdateBreakpoints</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>CCSTLinkCatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchMMERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchNOCPERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchCHRERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchSTATERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchBUSERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchINTERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchSFERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchHARDERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCSTLinkUsbSerialNoSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkJtagSpeedList</name>
          <version>2</version>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkDAPNumber</name>
          <state/>
        </option>
        <option>
          <name>CCSTLinkDebugAccessPortRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkUseServerSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkProbeList</name>
          <version>2</version>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkTargetVccEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>CCSTLinkTargetVoltage</name>
          <state>###Uninitialized###</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>THIRDPARTY_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>CThirdPartyDriverDll</name>
          <state>###Uninitialized###</state>
        </option>
        <option>
          <name>CThirdPartyLogFileCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CThirdPartyLogFileEditB</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>TIFET_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>1</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>CCMSPFetResetList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetTargetVccTypeDefault</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetTargetVoltage</name>
          <state>###Uninitialized###</state>
        </option>
        <option>
          <name>CCMSPFetVCCDefault</name>
          <state>1</state>
        </option>
        <option>
          <name>CCMSPFetTargetSettlingtime</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetRadioJtagSpeedType</name>
          <state>1</state>
        </option>
        <option>
          <name>CCMSPFetConnection</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetUsbComPort</name>
          <state>Automatic</state>
        </option>
        <option>
          <name>CCMSPFetAllowAccessToBSL</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCMSPFetRadioEraseFlash</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>XDS100_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>9</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>TIPackageOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>TIPackage</name>
          <state/>
        </option>
        <option>
          <name>BoardFile</name>
          <state/>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCXds100BreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100DoUpdateBreakpoints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100UpdateBreakpoints</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>CCXds100CatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchUndef</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchData</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchPrefetch</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchMMERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchNOCPERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchCHRERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchSTATERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchBUSERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchINTERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchSFERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchHARDERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CpuClockEdit</name>
          <state/>
        </option>
        <option>
          <name>CCXds100SwoClockAuto</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100SwoClockEdit</name>
          <state>1000</state>
        </option>
        <option>
          <name>CCXds100HWResetDelay</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100ResetList</name>
          <version>1</version>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100UsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCXds100UsbSerialNoSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100JtagSpeedList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100InterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100InterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100ProbeList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100SWOPortRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100SWOPort</name>
          <state>1</state>
        </option>
        <option>
          <name>CCXDSTargetVccEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXDSTargetVoltage</name>
          <state>###Uninitialized###</state>
        </option>
        <option>
          <name>OCXDSDigitalStatesConfigFile</name>
          <state>1</state>
        </option>
        <option>
          <name>OCSelectedCoreName</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <debuggerPlugins>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\Azure\AzureArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\CMX\CmxArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\CMX\CmxTinyArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\embOS\embOSPlugin.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\FreeRtos\FreeRtosArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\Mbed\MbedArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\Mbed\MbedArmPlugin2.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\OpenRTOS\OpenRTOSPlugin.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\SafeRTOS\SafeRTOSPlugin.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\SMX\smxAwareIarArm9a.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\ThreadX\ThreadXArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\uCOS-II\uCOS-II-286-KA-CSpy.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\uCOS-II\uCOS-II-KA-CSpy.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\uCOS-III\uCOS-III-KA-CSpy.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$EW_DIR$\common\plugins\Orti\Orti.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$EW_DIR$\common\plugins\TargetAccessServer\TargetAccessServer.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$EW_DIR$\common\plugins\uCProbe\uCProbePlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
    </debuggerPlugins>
  </configuration>
  <configuration>
    <name>int flash sramdata release</name>
    <toolchain>
      <name>ARM</name>
    </toolchain>
    <debug>0</debug>
    <settings>
      <name>C-SPY</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>33</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCOverrideSlave</name>
          <state>0</state>
        </option>
        <option>
          <name>OCOverrideSlavePath</name>
          <state/>
        </option>
        <option>
          <name>C_32_64Device</name>
          <state>1</state>
        </option>
        <option>
          <name>AuthEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>AuthSdmSelection</name>
          <state>1</state>
        </option>
        <option>
          <name>AuthSdmManifest</name>
          <state/>
        </option>
        <option>
          <name>AuthSdmExplicitLib</name>
          <state/>
        </option>
        <option>
          <name>AuthEnforce</name>
          <state>0</state>
        </option>
        <option>
          <name>CInput</name>
          <state>1</state>
        </option>
        <option>
          <name>CEndian</name>
          <state>1</state>
        </option>
        <option>
          <name>CProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>OCVariant</name>
          <state>0</state>
        </option>
        <option>
          <name>MacOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>MacFile</name>
          <state/>
        </option>
        <option>
          <name>MemOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>MemFile</name>
          <state>$TOOLKIT_DIR$\CONFIG\debugger\Freescale\MK70FN1M0xxx12.ddf</state>
        </option>
        <option>
          <name>RunToEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>RunToName</name>
          <state>main</state>
        </option>
        <option>
          <name>CExtraOptionsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CExtraOptions</name>
          <state/>
        </option>
        <option>
          <name>CFpuProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>OCDDFArgumentProducer</name>
          <state/>
        </option>
        <option>
          <name>OCDownloadSuppressDownload</name>
          <state>0</state>
        </option>
        <option>
          <name>OCDownloadVerifyAll</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProductVersion</name>
          <state>5.50.0.51907</state>
        </option>
        <option>
          <name>OCDynDriverList</name>
          <state>JLINK_ID</state>
        </option>
        <option>
          <name>OCLastSavedByProductVersion</name>
          <state>6.40.2.53991</state>
        </option>
        <option>
          <name>UseFlashLoader</name>
          <state>1</state>
        </option>
        <option>
          <name>CLowLevel</name>
          <state>1</state>
        </option>
        <option>
          <name>OCBE8Slave</name>
          <state>1</state>
        </option>
        <option>
          <name>MacFile2</name>
          <state/>
        </option>
        <option>
          <name>CDevice</name>
          <state>1</state>
        </option>
        <option>
          <name>FlashLoadersV3</name>
          <state>$TOOLKIT_DIR$\config\flashloader\Freescale\FlashK70Fxxx128K.board</state>
        </option>
        <option>
          <name>OCImagesSuppressCheck1</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesPath1</name>
          <state/>
        </option>
        <option>
          <name>OCImagesSuppressCheck2</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesPath2</name>
          <state/>
        </option>
        <option>
          <name>OCImagesSuppressCheck3</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesPath3</name>
          <state/>
        </option>
        <option>
          <name>OverrideDefFlashBoard</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesOffset1</name>
          <state/>
        </option>
        <option>
          <name>OCImagesOffset2</name>
          <state/>
        </option>
        <option>
          <name>OCImagesOffset3</name>
          <state/>
        </option>
        <option>
          <name>OCImagesUse1</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesUse2</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesUse3</name>
          <state>0</state>
        </option>
        <option>
          <name>OCDeviceConfigMacroFile</name>
          <state>1</state>
        </option>
        <option>
          <name>OCDebuggerExtraOption</name>
          <state>1</state>
        </option>
        <option>
          <name>OCAllMTBOptions</name>
          <state>1</state>
        </option>
        <option>
          <name>OCMulticoreNrOfCores</name>
          <state>1</state>
        </option>
        <option>
          <name>OCMulticoreWorkspace</name>
          <state/>
        </option>
        <option>
          <name>OCMulticoreSlaveProject</name>
          <state/>
        </option>
        <option>
          <name>OCMulticoreSlaveConfiguration</name>
          <state/>
        </option>
        <option>
          <name>OCDownloadExtraImage</name>
          <state>1</state>
        </option>
        <option>
          <name>OCAttachSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>MassEraseBeforeFlashing</name>
          <state>0</state>
        </option>
        <option>
          <name>OCMulticoreNrOfCoresSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>OCMulticoreAMPConfigType</name>
          <state>0</state>
        </option>
        <option>
          <name>OCMulticoreSessionFile</name>
          <state/>
        </option>
        <option>
          <name>OCTpiuBaseOption</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>ARMSIM_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>1</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCSimDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>OCSimEnablePSP</name>
          <state>0</state>
        </option>
        <option>
          <name>OCSimPspOverrideConfig</name>
          <state>0</state>
        </option>
        <option>
          <name>OCSimPspConfigFile</name>
          <state/>
        </option>
      </data>
    </settings>
    <settings>
      <name>CADI_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>CCadiMemory</name>
          <state>1</state>
        </option>
        <option>
          <name>Fast Model</name>
          <state/>
        </option>
        <option>
          <name>CCADILogFileCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CCADILogFileEditB</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>CMSISDAP_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>4</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>CatchSFERR</name>
          <state>1</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>OCIarProbeScriptFile</name>
          <state>1</state>
        </option>
        <option>
          <name>CMSISDAPResetList</name>
          <version>1</version>
          <state>10</state>
        </option>
        <option>
          <name>CMSISDAPHWResetDuration</name>
          <state>300</state>
        </option>
        <option>
          <name>CMSISDAPHWResetDelay</name>
          <state>200</state>
        </option>
        <option>
          <name>CMSISDAPDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CMSISDAPInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiTargetEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPJtagSpeedList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPRestoreBreakpointsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPUpdateBreakpointsEdit</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>RDICatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchUndef</name>
          <state>1</state>
        </option>
        <option>
          <name>RDICatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchData</name>
          <state>1</state>
        </option>
        <option>
          <name>RDICatchPrefetch</name>
          <state>1</state>
        </option>
        <option>
          <name>RDICatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchMMERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchNOCPERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchCHKERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchSTATERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchBUSERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchINTERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchHARDERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiCPUEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiCPUNumber</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeCfgOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeConfig</name>
          <state/>
        </option>
        <option>
          <name>CMSISDAPProbeConfigRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPSelectedCPUBehaviour</name>
          <state>0</state>
        </option>
        <option>
          <name>ICpuName</name>
          <state/>
        </option>
        <option>
          <name>OCJetEmuParams</name>
          <state>1</state>
        </option>
        <option>
          <name>CCCMSISDAPUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCCMSISDAPUsbSerialNoSelect</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>E2_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>E2PowerFromProbe</name>
          <state>1</state>
        </option>
        <option>
          <name>CE2UsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CE2IdCodeEditB</name>
          <state>0xFFFF'FFFF'FFFF'FFFF'FFFF'FFFF'FFFF'FFFF</state>
        </option>
        <option>
          <name>CE2LogFileCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CE2LogFileEditB</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>GDBSERVER_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>TCPIP</name>
          <state>aaa.bbb.ccc.ddd</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCJTagBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJTagDoUpdateBreakpoints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJTagUpdateBreakpoints</name>
          <state>_call_main</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>GPLINK_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>IJET_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>10</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>CatchSFERR</name>
          <state>1</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>OCIarProbeScriptFile</name>
          <state>1</state>
        </option>
        <option>
          <name>IjetResetList</name>
          <version>1</version>
          <state>10</state>
        </option>
        <option>
          <name>IjetHWResetDuration</name>
          <state>300</state>
        </option>
        <option>
          <name>IjetHWResetDelay</name>
          <state>200</state>
        </option>
        <option>
          <name>IjetPowerFromProbe</name>
          <state>1</state>
        </option>
        <option>
          <name>IjetPowerRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>IjetInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiTargetEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetScanChainNonARMDevices</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetIRLength</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetJtagSpeedList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>IjetProtocolRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetSwoPin</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetCpuClockEdit</name>
          <state>72.0</state>
        </option>
        <option>
          <name>IjetSwoPrescalerList</name>
          <version>1</version>
          <state>0</state>
        </option>
        <option>
          <name>IjetBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetRestoreBreakpointsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetUpdateBreakpointsEdit</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>RDICatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchUndef</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchData</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchPrefetch</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchMMERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchNOCPERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchCHKERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchSTATERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchBUSERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchINTERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchHARDERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeCfgOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeConfig</name>
          <state/>
        </option>
        <option>
          <name>IjetProbeConfigRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiCPUEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiCPUNumber</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetSelectedCPUBehaviour</name>
          <state>0</state>
        </option>
        <option>
          <name>ICpuName</name>
          <state/>
        </option>
        <option>
          <name>OCJetEmuParams</name>
          <state>1</state>
        </option>
        <option>
          <name>IjetPreferETB</name>
          <state>1</state>
        </option>
        <option>
          <name>IjetTraceSettingsList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>IjetTraceSizeList</name>
          <version>0</version>
          <state>4</state>
        </option>
        <option>
          <name>FlashBoardPathSlave</name>
          <state>0</state>
        </option>
        <option>
          <name>CCIjetUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCIjetUsbSerialNoSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARReset</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREREL1NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREREL1S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREREL2NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREREL3S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREEL1NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARREL1NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREEL1S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARREL1S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREEL2NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARREL2NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREEL3S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARREL3S</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetHWResetTimingOverride</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>JLINK_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>16</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>CCCatchSFERR</name>
          <state>0</state>
        </option>
        <option>
          <name>JLinkSpeed</name>
          <state>100</state>
        </option>
        <option>
          <name>CCJLinkDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCJLinkHWResetDelay</name>
          <state>0</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>JLinkInitialSpeed</name>
          <state>32</state>
        </option>
        <option>
          <name>CCDoJlinkMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>CCScanChainNonARMDevices</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkIRLength</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkCommRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkTCPIP</name>
          <state>*************</state>
        </option>
        <option>
          <name>CCJLinkSpeedRadioV2</name>
          <state>0</state>
        </option>
        <option>
          <name>CCUSBDevice</name>
          <version>1</version>
          <state>1</state>
        </option>
        <option>
          <name>CCRDICatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchUndef</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchData</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchPrefetch</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkDoUpdateBreakpoints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkUpdateBreakpoints</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>CCJLinkInterfaceRadio</name>
          <state>1</state>
        </option>
        <option>
          <name>CCJLinkResetList</name>
          <version>6</version>
          <state>7</state>
        </option>
        <option>
          <name>CCJLinkInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchMMERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchNOCPERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchCHRERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchSTATERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchBUSERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchINTERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchHARDERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>OCJLinkScriptFile</name>
          <state>1</state>
        </option>
        <option>
          <name>CCJLinkUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCTcpIpAlt</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkTcpIpSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCCpuClockEdit</name>
          <state>72.0</state>
        </option>
        <option>
          <name>CCSwoClockAuto</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSwoClockEdit</name>
          <state>2000</state>
        </option>
        <option>
          <name>OCJLinkTraceSource</name>
          <state>0</state>
        </option>
        <option>
          <name>OCJLinkTraceSourceDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>OCJLinkDeviceName</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>LMIFTDI_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>3</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>LmiftdiSpeed</name>
          <state>500</state>
        </option>
        <option>
          <name>CCLmiftdiDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLmiftdiLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCLmiFtdiInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLmiFtdiInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLmiftdiUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCLmiftdiUsbSerialNoSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLmiftdiResetList</name>
          <version>0</version>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>NULINK_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>PEMICRO_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>3</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>CCJPEMicroShowSettings</name>
          <state>0</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>STLINK_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>8</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>CCSTLinkInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkResetList</name>
          <version>3</version>
          <state>0</state>
        </option>
        <option>
          <name>CCCpuClockEdit</name>
          <state>72.0</state>
        </option>
        <option>
          <name>CCSwoClockAuto</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSwoClockEdit</name>
          <state>2000</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCSTLinkDoUpdateBreakpoints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkUpdateBreakpoints</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>CCSTLinkCatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchMMERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchNOCPERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchCHRERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchSTATERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchBUSERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchINTERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchSFERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchHARDERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCSTLinkUsbSerialNoSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkJtagSpeedList</name>
          <version>2</version>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkDAPNumber</name>
          <state/>
        </option>
        <option>
          <name>CCSTLinkDebugAccessPortRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkUseServerSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkProbeList</name>
          <version>2</version>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkTargetVccEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>CCSTLinkTargetVoltage</name>
          <state>###Uninitialized###</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>THIRDPARTY_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>CThirdPartyDriverDll</name>
          <state>###Uninitialized###</state>
        </option>
        <option>
          <name>CThirdPartyLogFileCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CThirdPartyLogFileEditB</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>TIFET_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>1</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>CCMSPFetResetList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetTargetVccTypeDefault</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetTargetVoltage</name>
          <state>###Uninitialized###</state>
        </option>
        <option>
          <name>CCMSPFetVCCDefault</name>
          <state>1</state>
        </option>
        <option>
          <name>CCMSPFetTargetSettlingtime</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetRadioJtagSpeedType</name>
          <state>1</state>
        </option>
        <option>
          <name>CCMSPFetConnection</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetUsbComPort</name>
          <state>Automatic</state>
        </option>
        <option>
          <name>CCMSPFetAllowAccessToBSL</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCMSPFetRadioEraseFlash</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>XDS100_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>9</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>TIPackageOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>TIPackage</name>
          <state/>
        </option>
        <option>
          <name>BoardFile</name>
          <state/>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCXds100BreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100DoUpdateBreakpoints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100UpdateBreakpoints</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>CCXds100CatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchUndef</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchData</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchPrefetch</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchMMERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchNOCPERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchCHRERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchSTATERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchBUSERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchINTERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchSFERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchHARDERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CpuClockEdit</name>
          <state/>
        </option>
        <option>
          <name>CCXds100SwoClockAuto</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100SwoClockEdit</name>
          <state>1000</state>
        </option>
        <option>
          <name>CCXds100HWResetDelay</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100ResetList</name>
          <version>1</version>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100UsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCXds100UsbSerialNoSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100JtagSpeedList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100InterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100InterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100ProbeList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100SWOPortRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100SWOPort</name>
          <state>1</state>
        </option>
        <option>
          <name>CCXDSTargetVccEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXDSTargetVoltage</name>
          <state>###Uninitialized###</state>
        </option>
        <option>
          <name>OCXDSDigitalStatesConfigFile</name>
          <state>1</state>
        </option>
        <option>
          <name>OCSelectedCoreName</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <debuggerPlugins>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\Azure\AzureArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\CMX\CmxArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\CMX\CmxTinyArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\embOS\embOSPlugin.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\FreeRtos\FreeRtosArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\Mbed\MbedArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\Mbed\MbedArmPlugin2.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\OpenRTOS\OpenRTOSPlugin.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\SafeRTOS\SafeRTOSPlugin.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\SMX\smxAwareIarArm9a.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\ThreadX\ThreadXArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\uCOS-II\uCOS-II-286-KA-CSpy.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\uCOS-II\uCOS-II-KA-CSpy.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\uCOS-III\uCOS-III-KA-CSpy.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$EW_DIR$\common\plugins\Orti\Orti.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$EW_DIR$\common\plugins\TargetAccessServer\TargetAccessServer.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$EW_DIR$\common\plugins\uCProbe\uCProbePlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
    </debuggerPlugins>
  </configuration>
  <configuration>
    <name>int flash ddrdata debug</name>
    <toolchain>
      <name>ARM</name>
    </toolchain>
    <debug>1</debug>
    <settings>
      <name>C-SPY</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>33</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCOverrideSlave</name>
          <state>0</state>
        </option>
        <option>
          <name>OCOverrideSlavePath</name>
          <state/>
        </option>
        <option>
          <name>C_32_64Device</name>
          <state>1</state>
        </option>
        <option>
          <name>AuthEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>AuthSdmSelection</name>
          <state>1</state>
        </option>
        <option>
          <name>AuthSdmManifest</name>
          <state/>
        </option>
        <option>
          <name>AuthSdmExplicitLib</name>
          <state/>
        </option>
        <option>
          <name>AuthEnforce</name>
          <state>0</state>
        </option>
        <option>
          <name>CInput</name>
          <state>1</state>
        </option>
        <option>
          <name>CEndian</name>
          <state>1</state>
        </option>
        <option>
          <name>CProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>OCVariant</name>
          <state>0</state>
        </option>
        <option>
          <name>MacOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>MacFile</name>
          <state/>
        </option>
        <option>
          <name>MemOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>MemFile</name>
          <state>$TOOLKIT_DIR$\CONFIG\debugger\Freescale\MK70FN1M0xxx12.ddf</state>
        </option>
        <option>
          <name>RunToEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>RunToName</name>
          <state>main</state>
        </option>
        <option>
          <name>CExtraOptionsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CExtraOptions</name>
          <state/>
        </option>
        <option>
          <name>CFpuProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>OCDDFArgumentProducer</name>
          <state/>
        </option>
        <option>
          <name>OCDownloadSuppressDownload</name>
          <state>0</state>
        </option>
        <option>
          <name>OCDownloadVerifyAll</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProductVersion</name>
          <state>5.50.0.51907</state>
        </option>
        <option>
          <name>OCDynDriverList</name>
          <state>JLINK_ID</state>
        </option>
        <option>
          <name>OCLastSavedByProductVersion</name>
          <state>6.40.2.53991</state>
        </option>
        <option>
          <name>UseFlashLoader</name>
          <state>1</state>
        </option>
        <option>
          <name>CLowLevel</name>
          <state>1</state>
        </option>
        <option>
          <name>OCBE8Slave</name>
          <state>1</state>
        </option>
        <option>
          <name>MacFile2</name>
          <state/>
        </option>
        <option>
          <name>CDevice</name>
          <state>1</state>
        </option>
        <option>
          <name>FlashLoadersV3</name>
          <state>$TOOLKIT_DIR$\config\flashloader\Freescale\FlashK70Fxxx128K.board</state>
        </option>
        <option>
          <name>OCImagesSuppressCheck1</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesPath1</name>
          <state/>
        </option>
        <option>
          <name>OCImagesSuppressCheck2</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesPath2</name>
          <state/>
        </option>
        <option>
          <name>OCImagesSuppressCheck3</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesPath3</name>
          <state/>
        </option>
        <option>
          <name>OverrideDefFlashBoard</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesOffset1</name>
          <state/>
        </option>
        <option>
          <name>OCImagesOffset2</name>
          <state/>
        </option>
        <option>
          <name>OCImagesOffset3</name>
          <state/>
        </option>
        <option>
          <name>OCImagesUse1</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesUse2</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesUse3</name>
          <state>0</state>
        </option>
        <option>
          <name>OCDeviceConfigMacroFile</name>
          <state>1</state>
        </option>
        <option>
          <name>OCDebuggerExtraOption</name>
          <state>1</state>
        </option>
        <option>
          <name>OCAllMTBOptions</name>
          <state>1</state>
        </option>
        <option>
          <name>OCMulticoreNrOfCores</name>
          <state>1</state>
        </option>
        <option>
          <name>OCMulticoreWorkspace</name>
          <state/>
        </option>
        <option>
          <name>OCMulticoreSlaveProject</name>
          <state/>
        </option>
        <option>
          <name>OCMulticoreSlaveConfiguration</name>
          <state/>
        </option>
        <option>
          <name>OCDownloadExtraImage</name>
          <state>1</state>
        </option>
        <option>
          <name>OCAttachSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>MassEraseBeforeFlashing</name>
          <state>0</state>
        </option>
        <option>
          <name>OCMulticoreNrOfCoresSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>OCMulticoreAMPConfigType</name>
          <state>0</state>
        </option>
        <option>
          <name>OCMulticoreSessionFile</name>
          <state/>
        </option>
        <option>
          <name>OCTpiuBaseOption</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>ARMSIM_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>1</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCSimDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>OCSimEnablePSP</name>
          <state>0</state>
        </option>
        <option>
          <name>OCSimPspOverrideConfig</name>
          <state>0</state>
        </option>
        <option>
          <name>OCSimPspConfigFile</name>
          <state/>
        </option>
      </data>
    </settings>
    <settings>
      <name>CADI_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>CCadiMemory</name>
          <state>1</state>
        </option>
        <option>
          <name>Fast Model</name>
          <state/>
        </option>
        <option>
          <name>CCADILogFileCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CCADILogFileEditB</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>CMSISDAP_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>4</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>CatchSFERR</name>
          <state>1</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>OCIarProbeScriptFile</name>
          <state>1</state>
        </option>
        <option>
          <name>CMSISDAPResetList</name>
          <version>1</version>
          <state>10</state>
        </option>
        <option>
          <name>CMSISDAPHWResetDuration</name>
          <state>300</state>
        </option>
        <option>
          <name>CMSISDAPHWResetDelay</name>
          <state>200</state>
        </option>
        <option>
          <name>CMSISDAPDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CMSISDAPInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiTargetEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPJtagSpeedList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPRestoreBreakpointsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPUpdateBreakpointsEdit</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>RDICatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchUndef</name>
          <state>1</state>
        </option>
        <option>
          <name>RDICatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchData</name>
          <state>1</state>
        </option>
        <option>
          <name>RDICatchPrefetch</name>
          <state>1</state>
        </option>
        <option>
          <name>RDICatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchMMERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchNOCPERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchCHKERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchSTATERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchBUSERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchINTERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchHARDERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiCPUEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiCPUNumber</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeCfgOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeConfig</name>
          <state/>
        </option>
        <option>
          <name>CMSISDAPProbeConfigRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPSelectedCPUBehaviour</name>
          <state>0</state>
        </option>
        <option>
          <name>ICpuName</name>
          <state/>
        </option>
        <option>
          <name>OCJetEmuParams</name>
          <state>1</state>
        </option>
        <option>
          <name>CCCMSISDAPUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCCMSISDAPUsbSerialNoSelect</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>E2_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>E2PowerFromProbe</name>
          <state>1</state>
        </option>
        <option>
          <name>CE2UsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CE2IdCodeEditB</name>
          <state>0xFFFF'FFFF'FFFF'FFFF'FFFF'FFFF'FFFF'FFFF</state>
        </option>
        <option>
          <name>CE2LogFileCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CE2LogFileEditB</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>GDBSERVER_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>TCPIP</name>
          <state>aaa.bbb.ccc.ddd</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCJTagBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJTagDoUpdateBreakpoints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJTagUpdateBreakpoints</name>
          <state>_call_main</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>GPLINK_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>IJET_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>10</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>CatchSFERR</name>
          <state>1</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>OCIarProbeScriptFile</name>
          <state>1</state>
        </option>
        <option>
          <name>IjetResetList</name>
          <version>1</version>
          <state>10</state>
        </option>
        <option>
          <name>IjetHWResetDuration</name>
          <state>300</state>
        </option>
        <option>
          <name>IjetHWResetDelay</name>
          <state>200</state>
        </option>
        <option>
          <name>IjetPowerFromProbe</name>
          <state>1</state>
        </option>
        <option>
          <name>IjetPowerRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>IjetInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiTargetEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetScanChainNonARMDevices</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetIRLength</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetJtagSpeedList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>IjetProtocolRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetSwoPin</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetCpuClockEdit</name>
          <state>72.0</state>
        </option>
        <option>
          <name>IjetSwoPrescalerList</name>
          <version>1</version>
          <state>0</state>
        </option>
        <option>
          <name>IjetBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetRestoreBreakpointsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetUpdateBreakpointsEdit</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>RDICatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchUndef</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchData</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchPrefetch</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchMMERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchNOCPERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchCHKERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchSTATERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchBUSERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchINTERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchHARDERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeCfgOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeConfig</name>
          <state/>
        </option>
        <option>
          <name>IjetProbeConfigRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiCPUEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiCPUNumber</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetSelectedCPUBehaviour</name>
          <state>0</state>
        </option>
        <option>
          <name>ICpuName</name>
          <state/>
        </option>
        <option>
          <name>OCJetEmuParams</name>
          <state>1</state>
        </option>
        <option>
          <name>IjetPreferETB</name>
          <state>1</state>
        </option>
        <option>
          <name>IjetTraceSettingsList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>IjetTraceSizeList</name>
          <version>0</version>
          <state>4</state>
        </option>
        <option>
          <name>FlashBoardPathSlave</name>
          <state>0</state>
        </option>
        <option>
          <name>CCIjetUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCIjetUsbSerialNoSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARReset</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREREL1NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREREL1S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREREL2NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREREL3S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREEL1NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARREL1NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREEL1S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARREL1S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREEL2NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARREL2NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREEL3S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARREL3S</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetHWResetTimingOverride</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>JLINK_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>16</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>CCCatchSFERR</name>
          <state>0</state>
        </option>
        <option>
          <name>JLinkSpeed</name>
          <state>100</state>
        </option>
        <option>
          <name>CCJLinkDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCJLinkHWResetDelay</name>
          <state>0</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>JLinkInitialSpeed</name>
          <state>32</state>
        </option>
        <option>
          <name>CCDoJlinkMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>CCScanChainNonARMDevices</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkIRLength</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkCommRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkTCPIP</name>
          <state/>
        </option>
        <option>
          <name>CCJLinkSpeedRadioV2</name>
          <state>0</state>
        </option>
        <option>
          <name>CCUSBDevice</name>
          <version>1</version>
          <state>1</state>
        </option>
        <option>
          <name>CCRDICatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchUndef</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchData</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchPrefetch</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkDoUpdateBreakpoints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkUpdateBreakpoints</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>CCJLinkInterfaceRadio</name>
          <state>1</state>
        </option>
        <option>
          <name>CCJLinkResetList</name>
          <version>6</version>
          <state>7</state>
        </option>
        <option>
          <name>CCJLinkInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchMMERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchNOCPERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchCHRERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchSTATERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchBUSERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchINTERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchHARDERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>OCJLinkScriptFile</name>
          <state>1</state>
        </option>
        <option>
          <name>CCJLinkUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCTcpIpAlt</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkTcpIpSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCCpuClockEdit</name>
          <state>72.0</state>
        </option>
        <option>
          <name>CCSwoClockAuto</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSwoClockEdit</name>
          <state>2000</state>
        </option>
        <option>
          <name>OCJLinkTraceSource</name>
          <state>0</state>
        </option>
        <option>
          <name>OCJLinkTraceSourceDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>OCJLinkDeviceName</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>LMIFTDI_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>3</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>LmiftdiSpeed</name>
          <state>500</state>
        </option>
        <option>
          <name>CCLmiftdiDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLmiftdiLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCLmiFtdiInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLmiFtdiInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLmiftdiUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCLmiftdiUsbSerialNoSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLmiftdiResetList</name>
          <version>0</version>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>NULINK_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>PEMICRO_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>3</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>CCJPEMicroShowSettings</name>
          <state>0</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>STLINK_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>8</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>CCSTLinkInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkResetList</name>
          <version>3</version>
          <state>0</state>
        </option>
        <option>
          <name>CCCpuClockEdit</name>
          <state>72.0</state>
        </option>
        <option>
          <name>CCSwoClockAuto</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSwoClockEdit</name>
          <state>2000</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCSTLinkDoUpdateBreakpoints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkUpdateBreakpoints</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>CCSTLinkCatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchMMERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchNOCPERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchCHRERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchSTATERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchBUSERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchINTERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchSFERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchHARDERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCSTLinkUsbSerialNoSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkJtagSpeedList</name>
          <version>2</version>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkDAPNumber</name>
          <state/>
        </option>
        <option>
          <name>CCSTLinkDebugAccessPortRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkUseServerSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkProbeList</name>
          <version>2</version>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkTargetVccEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>CCSTLinkTargetVoltage</name>
          <state>###Uninitialized###</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>THIRDPARTY_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>CThirdPartyDriverDll</name>
          <state>###Uninitialized###</state>
        </option>
        <option>
          <name>CThirdPartyLogFileCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CThirdPartyLogFileEditB</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>TIFET_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>1</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>CCMSPFetResetList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetTargetVccTypeDefault</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetTargetVoltage</name>
          <state>###Uninitialized###</state>
        </option>
        <option>
          <name>CCMSPFetVCCDefault</name>
          <state>1</state>
        </option>
        <option>
          <name>CCMSPFetTargetSettlingtime</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetRadioJtagSpeedType</name>
          <state>1</state>
        </option>
        <option>
          <name>CCMSPFetConnection</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetUsbComPort</name>
          <state>Automatic</state>
        </option>
        <option>
          <name>CCMSPFetAllowAccessToBSL</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCMSPFetRadioEraseFlash</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>XDS100_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>9</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>TIPackageOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>TIPackage</name>
          <state/>
        </option>
        <option>
          <name>BoardFile</name>
          <state/>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCXds100BreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100DoUpdateBreakpoints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100UpdateBreakpoints</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>CCXds100CatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchUndef</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchData</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchPrefetch</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchMMERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchNOCPERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchCHRERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchSTATERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchBUSERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchINTERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchSFERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchHARDERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CpuClockEdit</name>
          <state/>
        </option>
        <option>
          <name>CCXds100SwoClockAuto</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100SwoClockEdit</name>
          <state>1000</state>
        </option>
        <option>
          <name>CCXds100HWResetDelay</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100ResetList</name>
          <version>1</version>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100UsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCXds100UsbSerialNoSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100JtagSpeedList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100InterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100InterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100ProbeList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100SWOPortRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100SWOPort</name>
          <state>1</state>
        </option>
        <option>
          <name>CCXDSTargetVccEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXDSTargetVoltage</name>
          <state>###Uninitialized###</state>
        </option>
        <option>
          <name>OCXDSDigitalStatesConfigFile</name>
          <state>1</state>
        </option>
        <option>
          <name>OCSelectedCoreName</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <debuggerPlugins>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\Azure\AzureArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\CMX\CmxArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\CMX\CmxTinyArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\embOS\embOSPlugin.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\FreeRtos\FreeRtosArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\Mbed\MbedArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\Mbed\MbedArmPlugin2.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\OpenRTOS\OpenRTOSPlugin.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\SafeRTOS\SafeRTOSPlugin.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\SMX\smxAwareIarArm9a.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\ThreadX\ThreadXArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\uCOS-II\uCOS-II-286-KA-CSpy.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\uCOS-II\uCOS-II-KA-CSpy.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\uCOS-III\uCOS-III-KA-CSpy.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$EW_DIR$\common\plugins\Orti\Orti.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$EW_DIR$\common\plugins\TargetAccessServer\TargetAccessServer.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$EW_DIR$\common\plugins\uCProbe\uCProbePlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
    </debuggerPlugins>
  </configuration>
  <configuration>
    <name>int flash ddrdata release</name>
    <toolchain>
      <name>ARM</name>
    </toolchain>
    <debug>0</debug>
    <settings>
      <name>C-SPY</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>33</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCOverrideSlave</name>
          <state>0</state>
        </option>
        <option>
          <name>OCOverrideSlavePath</name>
          <state/>
        </option>
        <option>
          <name>C_32_64Device</name>
          <state>1</state>
        </option>
        <option>
          <name>AuthEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>AuthSdmSelection</name>
          <state>1</state>
        </option>
        <option>
          <name>AuthSdmManifest</name>
          <state/>
        </option>
        <option>
          <name>AuthSdmExplicitLib</name>
          <state/>
        </option>
        <option>
          <name>AuthEnforce</name>
          <state>0</state>
        </option>
        <option>
          <name>CInput</name>
          <state>1</state>
        </option>
        <option>
          <name>CEndian</name>
          <state>1</state>
        </option>
        <option>
          <name>CProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>OCVariant</name>
          <state>0</state>
        </option>
        <option>
          <name>MacOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>MacFile</name>
          <state/>
        </option>
        <option>
          <name>MemOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>MemFile</name>
          <state>$TOOLKIT_DIR$\CONFIG\debugger\Freescale\MK70FN1M0xxx12.ddf</state>
        </option>
        <option>
          <name>RunToEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>RunToName</name>
          <state>main</state>
        </option>
        <option>
          <name>CExtraOptionsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CExtraOptions</name>
          <state/>
        </option>
        <option>
          <name>CFpuProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>OCDDFArgumentProducer</name>
          <state/>
        </option>
        <option>
          <name>OCDownloadSuppressDownload</name>
          <state>0</state>
        </option>
        <option>
          <name>OCDownloadVerifyAll</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProductVersion</name>
          <state>5.50.0.51907</state>
        </option>
        <option>
          <name>OCDynDriverList</name>
          <state>JLINK_ID</state>
        </option>
        <option>
          <name>OCLastSavedByProductVersion</name>
          <state>6.40.2.53991</state>
        </option>
        <option>
          <name>UseFlashLoader</name>
          <state>1</state>
        </option>
        <option>
          <name>CLowLevel</name>
          <state>1</state>
        </option>
        <option>
          <name>OCBE8Slave</name>
          <state>1</state>
        </option>
        <option>
          <name>MacFile2</name>
          <state/>
        </option>
        <option>
          <name>CDevice</name>
          <state>1</state>
        </option>
        <option>
          <name>FlashLoadersV3</name>
          <state>$TOOLKIT_DIR$\config\flashloader\Freescale\FlashK70Fxxx128K.board</state>
        </option>
        <option>
          <name>OCImagesSuppressCheck1</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesPath1</name>
          <state/>
        </option>
        <option>
          <name>OCImagesSuppressCheck2</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesPath2</name>
          <state/>
        </option>
        <option>
          <name>OCImagesSuppressCheck3</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesPath3</name>
          <state/>
        </option>
        <option>
          <name>OverrideDefFlashBoard</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesOffset1</name>
          <state/>
        </option>
        <option>
          <name>OCImagesOffset2</name>
          <state/>
        </option>
        <option>
          <name>OCImagesOffset3</name>
          <state/>
        </option>
        <option>
          <name>OCImagesUse1</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesUse2</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesUse3</name>
          <state>0</state>
        </option>
        <option>
          <name>OCDeviceConfigMacroFile</name>
          <state>1</state>
        </option>
        <option>
          <name>OCDebuggerExtraOption</name>
          <state>1</state>
        </option>
        <option>
          <name>OCAllMTBOptions</name>
          <state>1</state>
        </option>
        <option>
          <name>OCMulticoreNrOfCores</name>
          <state>1</state>
        </option>
        <option>
          <name>OCMulticoreWorkspace</name>
          <state/>
        </option>
        <option>
          <name>OCMulticoreSlaveProject</name>
          <state/>
        </option>
        <option>
          <name>OCMulticoreSlaveConfiguration</name>
          <state/>
        </option>
        <option>
          <name>OCDownloadExtraImage</name>
          <state>1</state>
        </option>
        <option>
          <name>OCAttachSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>MassEraseBeforeFlashing</name>
          <state>0</state>
        </option>
        <option>
          <name>OCMulticoreNrOfCoresSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>OCMulticoreAMPConfigType</name>
          <state>0</state>
        </option>
        <option>
          <name>OCMulticoreSessionFile</name>
          <state/>
        </option>
        <option>
          <name>OCTpiuBaseOption</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>ARMSIM_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>1</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCSimDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>OCSimEnablePSP</name>
          <state>0</state>
        </option>
        <option>
          <name>OCSimPspOverrideConfig</name>
          <state>0</state>
        </option>
        <option>
          <name>OCSimPspConfigFile</name>
          <state/>
        </option>
      </data>
    </settings>
    <settings>
      <name>CADI_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>CCadiMemory</name>
          <state>1</state>
        </option>
        <option>
          <name>Fast Model</name>
          <state/>
        </option>
        <option>
          <name>CCADILogFileCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CCADILogFileEditB</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>CMSISDAP_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>4</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>CatchSFERR</name>
          <state>1</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>OCIarProbeScriptFile</name>
          <state>1</state>
        </option>
        <option>
          <name>CMSISDAPResetList</name>
          <version>1</version>
          <state>10</state>
        </option>
        <option>
          <name>CMSISDAPHWResetDuration</name>
          <state>300</state>
        </option>
        <option>
          <name>CMSISDAPHWResetDelay</name>
          <state>200</state>
        </option>
        <option>
          <name>CMSISDAPDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CMSISDAPInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiTargetEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPJtagSpeedList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPRestoreBreakpointsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPUpdateBreakpointsEdit</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>RDICatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchUndef</name>
          <state>1</state>
        </option>
        <option>
          <name>RDICatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchData</name>
          <state>1</state>
        </option>
        <option>
          <name>RDICatchPrefetch</name>
          <state>1</state>
        </option>
        <option>
          <name>RDICatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchMMERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchNOCPERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchCHKERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchSTATERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchBUSERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchINTERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchHARDERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiCPUEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiCPUNumber</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeCfgOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeConfig</name>
          <state/>
        </option>
        <option>
          <name>CMSISDAPProbeConfigRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPSelectedCPUBehaviour</name>
          <state>0</state>
        </option>
        <option>
          <name>ICpuName</name>
          <state/>
        </option>
        <option>
          <name>OCJetEmuParams</name>
          <state>1</state>
        </option>
        <option>
          <name>CCCMSISDAPUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCCMSISDAPUsbSerialNoSelect</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>E2_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>E2PowerFromProbe</name>
          <state>1</state>
        </option>
        <option>
          <name>CE2UsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CE2IdCodeEditB</name>
          <state>0xFFFF'FFFF'FFFF'FFFF'FFFF'FFFF'FFFF'FFFF</state>
        </option>
        <option>
          <name>CE2LogFileCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CE2LogFileEditB</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>GDBSERVER_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>TCPIP</name>
          <state>aaa.bbb.ccc.ddd</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCJTagBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJTagDoUpdateBreakpoints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJTagUpdateBreakpoints</name>
          <state>_call_main</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>GPLINK_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>IJET_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>10</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>CatchSFERR</name>
          <state>1</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>OCIarProbeScriptFile</name>
          <state>1</state>
        </option>
        <option>
          <name>IjetResetList</name>
          <version>1</version>
          <state>10</state>
        </option>
        <option>
          <name>IjetHWResetDuration</name>
          <state>300</state>
        </option>
        <option>
          <name>IjetHWResetDelay</name>
          <state>200</state>
        </option>
        <option>
          <name>IjetPowerFromProbe</name>
          <state>1</state>
        </option>
        <option>
          <name>IjetPowerRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>IjetInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiTargetEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetScanChainNonARMDevices</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetIRLength</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetJtagSpeedList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>IjetProtocolRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetSwoPin</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetCpuClockEdit</name>
          <state>72.0</state>
        </option>
        <option>
          <name>IjetSwoPrescalerList</name>
          <version>1</version>
          <state>0</state>
        </option>
        <option>
          <name>IjetBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetRestoreBreakpointsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetUpdateBreakpointsEdit</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>RDICatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchUndef</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchData</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchPrefetch</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchMMERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchNOCPERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchCHKERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchSTATERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchBUSERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchINTERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchHARDERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeCfgOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeConfig</name>
          <state/>
        </option>
        <option>
          <name>IjetProbeConfigRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiCPUEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiCPUNumber</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetSelectedCPUBehaviour</name>
          <state>0</state>
        </option>
        <option>
          <name>ICpuName</name>
          <state/>
        </option>
        <option>
          <name>OCJetEmuParams</name>
          <state>1</state>
        </option>
        <option>
          <name>IjetPreferETB</name>
          <state>1</state>
        </option>
        <option>
          <name>IjetTraceSettingsList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>IjetTraceSizeList</name>
          <version>0</version>
          <state>4</state>
        </option>
        <option>
          <name>FlashBoardPathSlave</name>
          <state>0</state>
        </option>
        <option>
          <name>CCIjetUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCIjetUsbSerialNoSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARReset</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREREL1NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREREL1S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREREL2NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREREL3S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREEL1NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARREL1NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREEL1S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARREL1S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREEL2NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARREL2NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREEL3S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARREL3S</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetHWResetTimingOverride</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>JLINK_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>16</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>CCCatchSFERR</name>
          <state>0</state>
        </option>
        <option>
          <name>JLinkSpeed</name>
          <state>100</state>
        </option>
        <option>
          <name>CCJLinkDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCJLinkHWResetDelay</name>
          <state>0</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>JLinkInitialSpeed</name>
          <state>32</state>
        </option>
        <option>
          <name>CCDoJlinkMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>CCScanChainNonARMDevices</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkIRLength</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkCommRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkTCPIP</name>
          <state>*************</state>
        </option>
        <option>
          <name>CCJLinkSpeedRadioV2</name>
          <state>0</state>
        </option>
        <option>
          <name>CCUSBDevice</name>
          <version>1</version>
          <state>1</state>
        </option>
        <option>
          <name>CCRDICatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchUndef</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchData</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchPrefetch</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkDoUpdateBreakpoints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkUpdateBreakpoints</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>CCJLinkInterfaceRadio</name>
          <state>1</state>
        </option>
        <option>
          <name>CCJLinkResetList</name>
          <version>6</version>
          <state>7</state>
        </option>
        <option>
          <name>CCJLinkInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchMMERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchNOCPERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchCHRERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchSTATERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchBUSERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchINTERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchHARDERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>OCJLinkScriptFile</name>
          <state>1</state>
        </option>
        <option>
          <name>CCJLinkUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCTcpIpAlt</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkTcpIpSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCCpuClockEdit</name>
          <state>72.0</state>
        </option>
        <option>
          <name>CCSwoClockAuto</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSwoClockEdit</name>
          <state>2000</state>
        </option>
        <option>
          <name>OCJLinkTraceSource</name>
          <state>0</state>
        </option>
        <option>
          <name>OCJLinkTraceSourceDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>OCJLinkDeviceName</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>LMIFTDI_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>3</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>LmiftdiSpeed</name>
          <state>500</state>
        </option>
        <option>
          <name>CCLmiftdiDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLmiftdiLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCLmiFtdiInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLmiFtdiInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLmiftdiUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCLmiftdiUsbSerialNoSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLmiftdiResetList</name>
          <version>0</version>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>NULINK_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>PEMICRO_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>3</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>CCJPEMicroShowSettings</name>
          <state>0</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>STLINK_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>8</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>CCSTLinkInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkResetList</name>
          <version>3</version>
          <state>0</state>
        </option>
        <option>
          <name>CCCpuClockEdit</name>
          <state>72.0</state>
        </option>
        <option>
          <name>CCSwoClockAuto</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSwoClockEdit</name>
          <state>2000</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCSTLinkDoUpdateBreakpoints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkUpdateBreakpoints</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>CCSTLinkCatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchMMERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchNOCPERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchCHRERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchSTATERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchBUSERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchINTERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchSFERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchHARDERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCSTLinkUsbSerialNoSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkJtagSpeedList</name>
          <version>2</version>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkDAPNumber</name>
          <state/>
        </option>
        <option>
          <name>CCSTLinkDebugAccessPortRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkUseServerSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkProbeList</name>
          <version>2</version>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkTargetVccEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>CCSTLinkTargetVoltage</name>
          <state>###Uninitialized###</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>THIRDPARTY_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>CThirdPartyDriverDll</name>
          <state>###Uninitialized###</state>
        </option>
        <option>
          <name>CThirdPartyLogFileCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CThirdPartyLogFileEditB</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>TIFET_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>1</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>CCMSPFetResetList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetTargetVccTypeDefault</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetTargetVoltage</name>
          <state>###Uninitialized###</state>
        </option>
        <option>
          <name>CCMSPFetVCCDefault</name>
          <state>1</state>
        </option>
        <option>
          <name>CCMSPFetTargetSettlingtime</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetRadioJtagSpeedType</name>
          <state>1</state>
        </option>
        <option>
          <name>CCMSPFetConnection</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetUsbComPort</name>
          <state>Automatic</state>
        </option>
        <option>
          <name>CCMSPFetAllowAccessToBSL</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCMSPFetRadioEraseFlash</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>XDS100_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>9</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>TIPackageOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>TIPackage</name>
          <state/>
        </option>
        <option>
          <name>BoardFile</name>
          <state/>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCXds100BreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100DoUpdateBreakpoints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100UpdateBreakpoints</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>CCXds100CatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchUndef</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchData</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchPrefetch</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchMMERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchNOCPERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchCHRERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchSTATERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchBUSERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchINTERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchSFERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchHARDERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CpuClockEdit</name>
          <state/>
        </option>
        <option>
          <name>CCXds100SwoClockAuto</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100SwoClockEdit</name>
          <state>1000</state>
        </option>
        <option>
          <name>CCXds100HWResetDelay</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100ResetList</name>
          <version>1</version>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100UsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCXds100UsbSerialNoSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100JtagSpeedList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100InterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100InterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100ProbeList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100SWOPortRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100SWOPort</name>
          <state>1</state>
        </option>
        <option>
          <name>CCXDSTargetVccEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXDSTargetVoltage</name>
          <state>###Uninitialized###</state>
        </option>
        <option>
          <name>OCXDSDigitalStatesConfigFile</name>
          <state>1</state>
        </option>
        <option>
          <name>OCSelectedCoreName</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <debuggerPlugins>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\Azure\AzureArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\CMX\CmxArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\CMX\CmxTinyArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\embOS\embOSPlugin.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\FreeRtos\FreeRtosArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\Mbed\MbedArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\Mbed\MbedArmPlugin2.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\OpenRTOS\OpenRTOSPlugin.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\SafeRTOS\SafeRTOSPlugin.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\SMX\smxAwareIarArm9a.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\ThreadX\ThreadXArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\uCOS-II\uCOS-II-286-KA-CSpy.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\uCOS-II\uCOS-II-KA-CSpy.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\uCOS-III\uCOS-III-KA-CSpy.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$EW_DIR$\common\plugins\Orti\Orti.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$EW_DIR$\common\plugins\TargetAccessServer\TargetAccessServer.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$EW_DIR$\common\plugins\uCProbe\uCProbePlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
    </debuggerPlugins>
  </configuration>
  <configuration>
    <name>int ram debug</name>
    <toolchain>
      <name>ARM</name>
    </toolchain>
    <debug>1</debug>
    <settings>
      <name>C-SPY</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>33</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCOverrideSlave</name>
          <state>0</state>
        </option>
        <option>
          <name>OCOverrideSlavePath</name>
          <state/>
        </option>
        <option>
          <name>C_32_64Device</name>
          <state>1</state>
        </option>
        <option>
          <name>AuthEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>AuthSdmSelection</name>
          <state>1</state>
        </option>
        <option>
          <name>AuthSdmManifest</name>
          <state/>
        </option>
        <option>
          <name>AuthSdmExplicitLib</name>
          <state/>
        </option>
        <option>
          <name>AuthEnforce</name>
          <state>0</state>
        </option>
        <option>
          <name>CInput</name>
          <state>1</state>
        </option>
        <option>
          <name>CEndian</name>
          <state>1</state>
        </option>
        <option>
          <name>CProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>OCVariant</name>
          <state>0</state>
        </option>
        <option>
          <name>MacOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>MacFile</name>
          <state/>
        </option>
        <option>
          <name>MemOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>MemFile</name>
          <state>$TOOLKIT_DIR$\CONFIG\debugger\Freescale\MK70FN1M0xxx12.ddf</state>
        </option>
        <option>
          <name>RunToEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>RunToName</name>
          <state>main</state>
        </option>
        <option>
          <name>CExtraOptionsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CExtraOptions</name>
          <state/>
        </option>
        <option>
          <name>CFpuProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>OCDDFArgumentProducer</name>
          <state/>
        </option>
        <option>
          <name>OCDownloadSuppressDownload</name>
          <state>0</state>
        </option>
        <option>
          <name>OCDownloadVerifyAll</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProductVersion</name>
          <state>5.50.0.51907</state>
        </option>
        <option>
          <name>OCDynDriverList</name>
          <state>JLINK_ID</state>
        </option>
        <option>
          <name>OCLastSavedByProductVersion</name>
          <state>6.40.2.53991</state>
        </option>
        <option>
          <name>UseFlashLoader</name>
          <state>0</state>
        </option>
        <option>
          <name>CLowLevel</name>
          <state>1</state>
        </option>
        <option>
          <name>OCBE8Slave</name>
          <state>1</state>
        </option>
        <option>
          <name>MacFile2</name>
          <state/>
        </option>
        <option>
          <name>CDevice</name>
          <state>1</state>
        </option>
        <option>
          <name>FlashLoadersV3</name>
          <state>$TOOLKIT_DIR$\config\flashloader\Freescale\FlashK70Fxxx128K.board</state>
        </option>
        <option>
          <name>OCImagesSuppressCheck1</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesPath1</name>
          <state/>
        </option>
        <option>
          <name>OCImagesSuppressCheck2</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesPath2</name>
          <state/>
        </option>
        <option>
          <name>OCImagesSuppressCheck3</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesPath3</name>
          <state/>
        </option>
        <option>
          <name>OverrideDefFlashBoard</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesOffset1</name>
          <state/>
        </option>
        <option>
          <name>OCImagesOffset2</name>
          <state/>
        </option>
        <option>
          <name>OCImagesOffset3</name>
          <state/>
        </option>
        <option>
          <name>OCImagesUse1</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesUse2</name>
          <state>0</state>
        </option>
        <option>
          <name>OCImagesUse3</name>
          <state>0</state>
        </option>
        <option>
          <name>OCDeviceConfigMacroFile</name>
          <state>1</state>
        </option>
        <option>
          <name>OCDebuggerExtraOption</name>
          <state>1</state>
        </option>
        <option>
          <name>OCAllMTBOptions</name>
          <state>1</state>
        </option>
        <option>
          <name>OCMulticoreNrOfCores</name>
          <state>1</state>
        </option>
        <option>
          <name>OCMulticoreWorkspace</name>
          <state/>
        </option>
        <option>
          <name>OCMulticoreSlaveProject</name>
          <state/>
        </option>
        <option>
          <name>OCMulticoreSlaveConfiguration</name>
          <state/>
        </option>
        <option>
          <name>OCDownloadExtraImage</name>
          <state>1</state>
        </option>
        <option>
          <name>OCAttachSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>MassEraseBeforeFlashing</name>
          <state>0</state>
        </option>
        <option>
          <name>OCMulticoreNrOfCoresSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>OCMulticoreAMPConfigType</name>
          <state>0</state>
        </option>
        <option>
          <name>OCMulticoreSessionFile</name>
          <state/>
        </option>
        <option>
          <name>OCTpiuBaseOption</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>ARMSIM_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>1</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCSimDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>OCSimEnablePSP</name>
          <state>0</state>
        </option>
        <option>
          <name>OCSimPspOverrideConfig</name>
          <state>0</state>
        </option>
        <option>
          <name>OCSimPspConfigFile</name>
          <state/>
        </option>
      </data>
    </settings>
    <settings>
      <name>CADI_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>CCadiMemory</name>
          <state>1</state>
        </option>
        <option>
          <name>Fast Model</name>
          <state/>
        </option>
        <option>
          <name>CCADILogFileCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CCADILogFileEditB</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>CMSISDAP_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>4</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>CatchSFERR</name>
          <state>1</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>OCIarProbeScriptFile</name>
          <state>1</state>
        </option>
        <option>
          <name>CMSISDAPResetList</name>
          <version>1</version>
          <state>10</state>
        </option>
        <option>
          <name>CMSISDAPHWResetDuration</name>
          <state>300</state>
        </option>
        <option>
          <name>CMSISDAPHWResetDelay</name>
          <state>200</state>
        </option>
        <option>
          <name>CMSISDAPDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CMSISDAPInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiTargetEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPJtagSpeedList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPRestoreBreakpointsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPUpdateBreakpointsEdit</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>RDICatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchUndef</name>
          <state>1</state>
        </option>
        <option>
          <name>RDICatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchData</name>
          <state>1</state>
        </option>
        <option>
          <name>RDICatchPrefetch</name>
          <state>1</state>
        </option>
        <option>
          <name>RDICatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchMMERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchNOCPERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchCHKERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchSTATERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchBUSERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchINTERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchHARDERR</name>
          <state>1</state>
        </option>
        <option>
          <name>CatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiCPUEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPMultiCPUNumber</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeCfgOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeConfig</name>
          <state/>
        </option>
        <option>
          <name>CMSISDAPProbeConfigRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CMSISDAPSelectedCPUBehaviour</name>
          <state>0</state>
        </option>
        <option>
          <name>ICpuName</name>
          <state/>
        </option>
        <option>
          <name>OCJetEmuParams</name>
          <state>1</state>
        </option>
        <option>
          <name>CCCMSISDAPUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCCMSISDAPUsbSerialNoSelect</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>E2_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>E2PowerFromProbe</name>
          <state>1</state>
        </option>
        <option>
          <name>CE2UsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CE2IdCodeEditB</name>
          <state>0xFFFF'FFFF'FFFF'FFFF'FFFF'FFFF'FFFF'FFFF</state>
        </option>
        <option>
          <name>CE2LogFileCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CE2LogFileEditB</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>GDBSERVER_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>TCPIP</name>
          <state>aaa.bbb.ccc.ddd</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCJTagBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJTagDoUpdateBreakpoints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJTagUpdateBreakpoints</name>
          <state>_call_main</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>GPLINK_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>IJET_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>10</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>CatchSFERR</name>
          <state>1</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>OCIarProbeScriptFile</name>
          <state>1</state>
        </option>
        <option>
          <name>IjetResetList</name>
          <version>1</version>
          <state>10</state>
        </option>
        <option>
          <name>IjetHWResetDuration</name>
          <state>300</state>
        </option>
        <option>
          <name>IjetHWResetDelay</name>
          <state>200</state>
        </option>
        <option>
          <name>IjetPowerFromProbe</name>
          <state>1</state>
        </option>
        <option>
          <name>IjetPowerRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>IjetInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiTargetEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetScanChainNonARMDevices</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetIRLength</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetJtagSpeedList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>IjetProtocolRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetSwoPin</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetCpuClockEdit</name>
          <state>72.0</state>
        </option>
        <option>
          <name>IjetSwoPrescalerList</name>
          <version>1</version>
          <state>0</state>
        </option>
        <option>
          <name>IjetBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetRestoreBreakpointsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetUpdateBreakpointsEdit</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>RDICatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchUndef</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchData</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchPrefetch</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>RDICatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchMMERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchNOCPERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchCHKERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchSTATERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchBUSERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchINTERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchHARDERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeCfgOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>OCProbeConfig</name>
          <state/>
        </option>
        <option>
          <name>IjetProbeConfigRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiCPUEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetMultiCPUNumber</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetSelectedCPUBehaviour</name>
          <state>0</state>
        </option>
        <option>
          <name>ICpuName</name>
          <state/>
        </option>
        <option>
          <name>OCJetEmuParams</name>
          <state>1</state>
        </option>
        <option>
          <name>IjetPreferETB</name>
          <state>1</state>
        </option>
        <option>
          <name>IjetTraceSettingsList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>IjetTraceSizeList</name>
          <version>0</version>
          <state>4</state>
        </option>
        <option>
          <name>FlashBoardPathSlave</name>
          <state>0</state>
        </option>
        <option>
          <name>CCIjetUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCIjetUsbSerialNoSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARReset</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREREL1NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREREL1S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREREL2NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREREL3S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREEL1NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARREL1NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREEL1S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARREL1S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREEL2NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARREL2NS</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8AREEL3S</name>
          <state>0</state>
        </option>
        <option>
          <name>CatchV8ARREL3S</name>
          <state>0</state>
        </option>
        <option>
          <name>IjetHWResetTimingOverride</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>JLINK_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>16</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>CCCatchSFERR</name>
          <state>0</state>
        </option>
        <option>
          <name>JLinkSpeed</name>
          <state>100</state>
        </option>
        <option>
          <name>CCJLinkDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCJLinkHWResetDelay</name>
          <state>0</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>JLinkInitialSpeed</name>
          <state>32</state>
        </option>
        <option>
          <name>CCDoJlinkMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>CCScanChainNonARMDevices</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkMultiTarget</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkIRLength</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkCommRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkTCPIP</name>
          <state/>
        </option>
        <option>
          <name>CCJLinkSpeedRadioV2</name>
          <state>0</state>
        </option>
        <option>
          <name>CCUSBDevice</name>
          <version>1</version>
          <state>1</state>
        </option>
        <option>
          <name>CCRDICatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchUndef</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchData</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchPrefetch</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CCRDICatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkBreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkDoUpdateBreakpoints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkUpdateBreakpoints</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>CCJLinkInterfaceRadio</name>
          <state>1</state>
        </option>
        <option>
          <name>CCJLinkResetList</name>
          <version>6</version>
          <state>7</state>
        </option>
        <option>
          <name>CCJLinkInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchMMERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchNOCPERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchCHRERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchSTATERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchBUSERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchINTERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchHARDERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>OCJLinkScriptFile</name>
          <state>1</state>
        </option>
        <option>
          <name>CCJLinkUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCTcpIpAlt</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCJLinkTcpIpSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCCpuClockEdit</name>
          <state>72.0</state>
        </option>
        <option>
          <name>CCSwoClockAuto</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSwoClockEdit</name>
          <state>2000</state>
        </option>
        <option>
          <name>OCJLinkTraceSource</name>
          <state>0</state>
        </option>
        <option>
          <name>OCJLinkTraceSourceDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>OCJLinkDeviceName</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>LMIFTDI_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>3</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>LmiftdiSpeed</name>
          <state>500</state>
        </option>
        <option>
          <name>CCLmiftdiDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLmiftdiLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCLmiFtdiInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLmiFtdiInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLmiftdiUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCLmiftdiUsbSerialNoSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CCLmiftdiResetList</name>
          <version>0</version>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>NULINK_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>PEMICRO_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>3</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>CCJPEMicroShowSettings</name>
          <state>0</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>STLINK_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>8</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>CCSTLinkInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkResetList</name>
          <version>3</version>
          <state>0</state>
        </option>
        <option>
          <name>CCCpuClockEdit</name>
          <state>72.0</state>
        </option>
        <option>
          <name>CCSwoClockAuto</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSwoClockEdit</name>
          <state>2000</state>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCSTLinkDoUpdateBreakpoints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkUpdateBreakpoints</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>CCSTLinkCatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchMMERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchNOCPERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchCHRERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchSTATERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchBUSERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchINTERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchSFERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchHARDERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkCatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkUsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCSTLinkUsbSerialNoSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkJtagSpeedList</name>
          <version>2</version>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkDAPNumber</name>
          <state/>
        </option>
        <option>
          <name>CCSTLinkDebugAccessPortRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkUseServerSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkProbeList</name>
          <version>2</version>
          <state>0</state>
        </option>
        <option>
          <name>CCSTLinkTargetVccEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>CCSTLinkTargetVoltage</name>
          <state>###Uninitialized###</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>THIRDPARTY_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>CThirdPartyDriverDll</name>
          <state>###Uninitialized###</state>
        </option>
        <option>
          <name>CThirdPartyLogFileCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CThirdPartyLogFileEditB</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>TIFET_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>1</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>CCMSPFetResetList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetInterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetInterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetTargetVccTypeDefault</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetTargetVoltage</name>
          <state>###Uninitialized###</state>
        </option>
        <option>
          <name>CCMSPFetVCCDefault</name>
          <state>1</state>
        </option>
        <option>
          <name>CCMSPFetTargetSettlingtime</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetRadioJtagSpeedType</name>
          <state>1</state>
        </option>
        <option>
          <name>CCMSPFetConnection</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetUsbComPort</name>
          <state>Automatic</state>
        </option>
        <option>
          <name>CCMSPFetAllowAccessToBSL</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetDoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCMSPFetLogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCMSPFetRadioEraseFlash</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>XDS100_ID</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>9</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OCDriverInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>TIPackageOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>TIPackage</name>
          <state/>
        </option>
        <option>
          <name>BoardFile</name>
          <state/>
        </option>
        <option>
          <name>DoLogfile</name>
          <state>0</state>
        </option>
        <option>
          <name>LogFile</name>
          <state>$PROJ_DIR$\cspycomm.log</state>
        </option>
        <option>
          <name>CCXds100BreakpointRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100DoUpdateBreakpoints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100UpdateBreakpoints</name>
          <state>_call_main</state>
        </option>
        <option>
          <name>CCXds100CatchReset</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchUndef</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchSWI</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchData</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchPrefetch</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchIRQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchFIQ</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchCORERESET</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchMMERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchNOCPERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchCHRERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchSTATERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchBUSERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchINTERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchSFERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchHARDERR</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CatchDummy</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100CpuClockEdit</name>
          <state/>
        </option>
        <option>
          <name>CCXds100SwoClockAuto</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100SwoClockEdit</name>
          <state>1000</state>
        </option>
        <option>
          <name>CCXds100HWResetDelay</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100ResetList</name>
          <version>1</version>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100UsbSerialNo</name>
          <state/>
        </option>
        <option>
          <name>CCXds100UsbSerialNoSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100JtagSpeedList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100InterfaceRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100InterfaceCmdLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100ProbeList</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100SWOPortRadio</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXds100SWOPort</name>
          <state>1</state>
        </option>
        <option>
          <name>CCXDSTargetVccEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>CCXDSTargetVoltage</name>
          <state>###Uninitialized###</state>
        </option>
        <option>
          <name>OCXDSDigitalStatesConfigFile</name>
          <state>1</state>
        </option>
        <option>
          <name>OCSelectedCoreName</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <debuggerPlugins>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\Azure\AzureArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\CMX\CmxArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\CMX\CmxTinyArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\embOS\embOSPlugin.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\FreeRtos\FreeRtosArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\Mbed\MbedArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\Mbed\MbedArmPlugin2.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\OpenRTOS\OpenRTOSPlugin.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\SafeRTOS\SafeRTOSPlugin.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\SMX\smxAwareIarArm9a.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\ThreadX\ThreadXArmPlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\uCOS-II\uCOS-II-286-KA-CSpy.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\uCOS-II\uCOS-II-KA-CSpy.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$TOOLKIT_DIR$\plugins\rtos\uCOS-III\uCOS-III-KA-CSpy.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$EW_DIR$\common\plugins\Orti\Orti.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$EW_DIR$\common\plugins\TargetAccessServer\TargetAccessServer.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
      <plugin>
        <file>$EW_DIR$\common\plugins\uCProbe\uCProbePlugin.ENU.ewplugin</file>
        <loadFlag>0</loadFlag>
      </plugin>
    </debuggerPlugins>
  </configuration>
</project>
