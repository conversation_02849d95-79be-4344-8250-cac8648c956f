#!armclang --target=arm-arm-none-eabi -march=armv8-m.main -E -x c
/*
** ###################################################################
**     Processors:          MCXA153VFM
**                          MCXA153VFT
**                          MCXA153VLF
**                          MCXA153VLH
**
**     Compiler:            Keil ARM C/C++ Compiler
**     Reference manual:    MCXA1 User manual
**     Version:             rev. 1.0, 2022-03-29
**     Build:               b240704
**
**     Abstract:
**         Linker file for the Keil ARM C/C++ Compiler
**
**     Copyright 2016 Freescale Semiconductor, Inc.
**     Copyright 2016-2024 NXP
**     SPDX-License-Identifier: BSD-3-Clause
**
**     http:                 www.nxp.com
**     mail:                 <EMAIL>
**
** ###################################################################
*/


/* Sizes */
#if (defined(__stack_size__))
  #define Stack_Size                   __stack_size__
#else
  #define Stack_Size                   0x0400
#endif

#if (defined(__heap_size__))
  #define Heap_Size                    __heap_size__
#else
  #define Heap_Size                    0x0400
#endif

#define  m_interrupts_start            0x00000000
#define  m_interrupts_size             0x00000200

#define  m_text_start                  0x00000200
#define  m_text_size                   0x0001FE00

#define  m_data_start                  0x20000000
#define  m_data_size                   0x00006000

#define  m_sramx0_start                0x04000000;
#define  m_sramx0_size                 0x00002000;

LR_m_text m_interrupts_start m_interrupts_size+m_text_size {   ; load region size_region

  VECTOR_ROM m_interrupts_start m_interrupts_size { ; load address = execution address
    * (.isr_vector,+FIRST)
  }

  ER_m_text m_text_start FIXED m_text_size { ; load address = execution address
    * (InRoot$$Sections)
    .ANY (+RO)
  }

  RW_m_data m_data_start m_data_size-Stack_Size-Heap_Size { ; RW data
    .ANY (+RW +ZI)
  }
  ARM_LIB_HEAP +0 EMPTY Heap_Size {    ; Heap region growing up
  }
  ARM_LIB_STACK m_data_start+m_data_size EMPTY -Stack_Size { ; Stack region growing down
  }

}
