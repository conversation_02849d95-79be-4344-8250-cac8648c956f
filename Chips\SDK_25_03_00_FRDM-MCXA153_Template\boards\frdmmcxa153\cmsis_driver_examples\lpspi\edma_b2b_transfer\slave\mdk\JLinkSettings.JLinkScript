/*********************************************************************
*            (c) 1995 - 2018 SEGGER Microcontroller GmbH             *
*                        The Embedded Experts                        *
*                           www.segger.com                           *
**********************************************************************

-------------------------- END-OF-HEADER -----------------------------
*/

void ConfigDebugMailbox(void)
{
  int value;
  JLINK_SYS_Report("*** ConfigDebugMailbox start");
  
  // Read AP ID register to identify DM AP at index 1
  JLINK_CORESIGHT_WriteDP(2, 0x010000f0);
  // The returned AP ID should be 0x002A0000
  value = JLINK_CORESIGHT_ReadAP(3);
  JLINK_SYS_Report1("AP ID:", value);

  // Select DM AP index 1
  JLINK_CORESIGHT_WriteDP(2, 0x01000000);
  JLINK_CORESIGHT_ReadDP(0);

  // Active DebugMailbox, Write DM RESYNC_REQ + CHIP_RESET_REQ
  JLINK_CORESIGHT_WriteAP(0, 0x21);

  // Poll CSW register (0) for zero return, indicating success
  value = -1;
  while (value != 0)
  {
    value = JLINK_CORESIGHT_ReadAP(0);  
  }
  JLINK_SYS_Report1("RESYNC_REQ + CHIP_RESET_REQ:", value);
  
  // Start DM-AP
  JLINK_CORESIGHT_WriteAP(1, 0x01);
  // Poll RETURN register (2) for zero return
  value = -1;
  while (value != 0)
  {
    value = JLINK_CORESIGHT_ReadAP(2) & 0xFFFF;  
  }
  JLINK_SYS_Report1("Start DM-AP:", value);

  // Enter Debug Session,  Write DM START_DBG_SESSION to REQUEST register (1)
  JLINK_CORESIGHT_WriteAP(1, 0x07);
  // Poll RETURN register (2) for zero return
  value = -1;
  while (value != 0)
  {
    value = JLINK_CORESIGHT_ReadAP(2) & 0xFFFF;  
  }
  JLINK_SYS_Report1("DEBUG_SESSION_REQ:", value);
  
  JLINK_SYS_Report("*** ConfigDebugMailbox end");
}

void AfterResetTarget(void) 
{
  int value;
  JLINK_TARGET_Halt(); // Make sure that the CPU is halted when reset is called

  value = JLINK_TARGET_IsHalted();
  if (value == 1) {
    JLINK_SYS_Report("Target is halted!");
  } else {
  JLINK_SYS_Report("Target is not halted, re-config debug mailbox!");
  ConfigDebugMailbox();
  JLINK_TARGET_Halt();  
  }

  JLINK_SYS_Report("*** Update SRAM_XEN_DP");
  
  // WriteEN index2
  JLINK_MEM_WriteU32(0x40091D00, 0x00060000);
  JLINK_MEM_WriteU32(0x40091D00, 0x00020002);
  JLINK_MEM_WriteU32(0x40091D00, 0x00010002);
  JLINK_MEM_WriteU32(0x40091D04, 0x00290000);
  JLINK_MEM_WriteU32(0x40091D00, 0x00020002);
  JLINK_MEM_WriteU32(0x40091D04, 0x00280000);
  JLINK_MEM_WriteU32(0x40091D00, 0x00000002);
  //SRAM_XEN=0xF
  JLINK_MEM_WriteU32(0x40091E58, 0x0000000F);
  JLINK_MEM_WriteU32(0x40091E5C, 0x0000000F);

  JLINK_SYS_Report("*** Update MEM0_BLK_CFG_W");
  // WriteEN index15
  JLINK_MEM_WriteU32(0x40091D00, 0x00060000);
  JLINK_MEM_WriteU32(0x40091D00, 0x0002000F);
  JLINK_MEM_WriteU32(0x40091D00, 0x0001000F);
  JLINK_MEM_WriteU32(0x40091D04, 0x00290000);
  JLINK_MEM_WriteU32(0x40091D00, 0x0002000F);
  JLINK_MEM_WriteU32(0x40091D04, 0x00280000);
  JLINK_MEM_WriteU32(0x40091D00, 0x0000000F);
  //MBC0_MEMN_GLBAC0=0x00007777, RWX
  JLINK_MEM_WriteU32(0x4008E020, 0x00007777);
  //For Flash RWX
  //MBC0_DOM0_MEM0_BLK_CFG_W0=0x00000000
  //MBC0_DOM0_MEM0_BLK_CFG_W1=0x00000000
  JLINK_MEM_WriteU32(0x4008E040, 0x00000000);
  JLINK_MEM_WriteU32(0x4008E044, 0x00000000);
  //For IFR0 RWX
  //MBC0_DOM_MEM1_BLK_CFG_W0=0x00000000
  JLINK_MEM_WriteU32(0x4008E180, 0x00000000);
}

/*********************************************************************
*
*       ResetTarget
*/
void ResetTarget(void) {
  int value;
  CORESIGHT_IndexAHBAPToUse = 0;
  
  JLINK_TARGET_Halt(); // Make sure that the CPU is halted when reset is called
  
  JLINK_SYS_Report("*** ResetTarget");
  
  // Set watch point
  JLINK_MEM_WriteU32(0xE0001020, 0x00000000);
  JLINK_MEM_WriteU32(0xE0001028, 0xF0000412);
  JLINK_MEM_WriteU32(0xE0001030, 0x0001FFFF);
  JLINK_MEM_WriteU32(0xE0001038, 0xF0000403);
  
  //// JLINK_MEM_WriteU32(0xE0001020, 0x40091040);
  //// JLINK_MEM_WriteU32(0xE0001028, 0xF0000412);
  //// JLINK_MEM_WriteU32(0xE0001030, 0x40091040);
  //// JLINK_MEM_WriteU32(0xE0001038, 0xF0000403);
  
  // Execute SYSRESETREQ via AIRCR
  JLINK_MEM_WriteU32(0xE000ED0C, 0x05FA0004);  
  JLINK_SYS_Sleep(100);
 
  JLINK_TARGET_Halt(); // Make sure that the CPU is halted when reset is called
  JLINK_MEM_WriteU32(0xE0001020, 0);
  JLINK_MEM_WriteU32(0xE0001028, 0);
  JLINK_MEM_WriteU32(0xE0001030, 0);
  JLINK_MEM_WriteU32(0xE0001038, 0);
  
  value = JLINK_TARGET_IsHalted();
  if (value == 1) {
    JLINK_SYS_Report("Target is halted!");
  } else {
  JLINK_SYS_Report("Target is not halted, re-config debug mailbox!");
  ConfigDebugMailbox();
  JLINK_TARGET_Halt();  
  }
}

/*********************************************************************
*
*       InitTarget
*/
void InitTarget(void) 
{

  JLINK_SYS_Report("******************************************************");
  JLINK_SYS_Report("J-Link script: MCX Cortex-M33 core J-Link script");
  JLINK_SYS_Report("******************************************************");
  JLINK_CORESIGHT_Configure("IRPre=0;DRPre=0;IRPost=0;DRPost=0;IRLenDevice=4");
  CPU = CORTEX_M33;      // Pre-select that we have a Cortex-M33 connected
  JTAG_AllowTAPReset = 0;   // J-Link is allowed to use a TAP reset for JTAG-chain auto-detection

  ////  JTAG_SetDeviceId(0, 0x6BA02477);  // 4-bits IRLen

  if (JLINK_ActiveTIF == JLINK_TIF_JTAG) {
    // JTAG Specific Part
    // Init AP Transfer Mode, Transaction Counter, and Lane Mask (Normal Transfer Mode, Include all Byte Lanes)
    // Additionally clear STICKYORUN, STICKYCMP, and STICKYERR bits by writing '1'
    JLINK_CORESIGHT_WriteDP(1, 0x50000F32);
  } else {
    // SWD Specific Part
    // Init AP Transfer Mode, Transaction Counter, and Lane Mask (Normal Transfer Mode, Include all Byte Lanes)
    JLINK_CORESIGHT_WriteDP(1, 0x50000F00);
    // Clear WDATAERR, STICKYORUN, STICKYCMP, and STICKYERR bits of CTRL/STAT Register by write to ABORT register
    JLINK_CORESIGHT_WriteDP(0, 0x0000001E);
  }

  ConfigDebugMailbox();

  CORESIGHT_IndexAHBAPToUse = 0;
}
