<?xml version="1.0" encoding="UTF-8"?>
<project>
  <fileVersion>4</fileVersion>
  <configuration>
    <name>debug</name>
    <toolchain>
      <name>ARM</name>
    </toolchain>
    <debug>1</debug>
    <settings>
      <name>General</name>
      <archiveVersion>3</archiveVersion>
      <data>
        <version>37</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OG_32_64DeviceCoreSlave</name>
          <version>34</version>
          <state>0</state>
        </option>
        <option>
          <name>BrowseInfoPath</name>
          <state>Debug\BrowseInfo</state>
        </option>
        <option>
          <name>ExePath</name>
          <state>$PROJ_DIR$/debug</state>
        </option>
        <option>
          <name>ObjPath</name>
          <state>$PROJ_DIR$/debug/obj</state>
        </option>
        <option>
          <name>ListPath</name>
          <state>$PROJ_DIR$/debug/list</state>
        </option>
        <option>
          <name>GEndianMode</name>
          <state>0</state>
        </option>
        <option>
          <name>Input description</name>
          <state>No specifier n, no float nor long long, no scan set, no assignment suppressing, without multibyte support.</state>
        </option>
        <option>
          <name>Output description</name>
          <state>No specifier a, A, no specifier n, no float nor long long, without multibyte support.</state>
        </option>
        <option>
          <name>GOutputBinary</name>
          <state>0</state>
        </option>
        <option>
          <name>OGCoreOrChip</name>
          <state>1</state>
        </option>
        <option>
          <name>GRuntimeLibSelect</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>GRuntimeLibSelectSlave</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>RTDescription</name>
          <state>0</state>
        </option>
        <option>
          <name>OGProductVersion</name>
          <state>6.50.6.4952</state>
        </option>
        <option>
          <name>OGLastSavedByProductVersion</name>
          <state>9.60.1.2936</state>
        </option>
        <option>
          <name>OGChipSelectEditMenu</name>
          <state>MCXA153	NXP MCXA153</state>
        </option>
        <option>
          <name>GenLowLevelInterface</name>
          <state>1</state>
        </option>
        <option>
          <name>GEndianModeBE</name>
          <state>1</state>
        </option>
        <option>
          <name>OGBufferedTerminalOutput</name>
          <state>0</state>
        </option>
        <option>
          <name>GenStdoutInterface</name>
          <state>0</state>
        </option>
        <option>
          <name>RTConfigPath2</name>
          <state>$TOOLKIT_DIR$\inc\c\DLib_Config_Normal.h</state>
        </option>
        <option>
          <name>GBECoreSlave</name>
          <version>34</version>
          <state>58</state>
        </option>
        <option>
          <name>OGUseCmsis</name>
          <state>0</state>
        </option>
        <option>
          <name>OGUseCmsisDspLib</name>
          <state>0</state>
        </option>
        <option>
          <name>GRuntimeLibThreads</name>
          <state>0</state>
        </option>
        <option>
          <name>CoreVariant</name>
          <version>34</version>
          <state>58</state>
        </option>
        <option>
          <name>GFPUDeviceSlave</name>
          <state>MCXA153	NXP MCXA153</state>
        </option>
        <option>
          <name>FPU2</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>NrRegs</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>NEON</name>
          <state>0</state>
        </option>
        <option>
          <name>GFPUCoreSlave2</name>
          <version>34</version>
          <state>58</state>
        </option>
        <option>
          <name>OGCMSISPackSelectDevice</name>
        </option>
        <option>
          <name>OgLibHeap</name>
          <state>0</state>
        </option>
        <option>
          <name>OGLibAdditionalLocale</name>
          <state>0</state>
        </option>
        <option>
          <name>OGPrintfVariant</name>
          <version>0</version>
          <state>3</state>
        </option>
        <option>
          <name>OGPrintfMultibyteSupport</name>
          <state>0</state>
        </option>
        <option>
          <name>OGScanfVariant</name>
          <version>0</version>
          <state>3</state>
        </option>
        <option>
          <name>OGScanfMultibyteSupport</name>
          <state>0</state>
        </option>
        <option>
          <name>GenLocaleTags</name>
          <state/>
        </option>
        <option>
          <name>GenLocaleDisplayOnly</name>
          <state/>
        </option>
        <option>
          <name>DSPExtension</name>
          <state>0</state>
        </option>
        <option>
          <name>TrustZone</name>
          <state>0</state>
        </option>
        <option>
          <name>TrustZoneModes</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>OGAarch64Abi</name>
          <state>0</state>
        </option>
        <option>
          <name>OG_32_64Device</name>
          <state>0</state>
        </option>
        <option>
          <name>BuildFilesPath</name>
          <state>Debug</state>
        </option>
        <option>
          <name>PointerAuthentication</name>
          <state>0</state>
        </option>
        <option>
          <name>FPU64</name>
          <state>1</state>
        </option>
        <option>
          <name>GOutputSo</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>ICCARM</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>39</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>CCPointerAutentiction</name>
          <state>0</state>
        </option>
        <option>
          <name>CCBranchTargetIdentification</name>
          <state>0</state>
        </option>
        <option>
          <name>CCDefines</name>
          <state>DEBUG</state>
          <state>SDK_DEBUGCONSOLE=1</state>
          <state>MCUX_META_BUILD</state>
          <state>MCUXPRESSO_SDK</state>
          <state>CPU_MCXA153VLH</state>
        </option>
        <option>
          <name>CCPreprocFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPreprocComments</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPreprocLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListCFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListCMnemonics</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListCMessages</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListAssFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListAssSource</name>
          <state>0</state>
        </option>
        <option>
          <name>CCEnableRemarks</name>
          <state>0</state>
        </option>
        <option>
          <name>CCDiagSuppress</name>
          <state/>
        </option>
        <option>
          <name>CCDiagRemark</name>
          <state/>
        </option>
        <option>
          <name>CCDiagWarning</name>
          <state/>
        </option>
        <option>
          <name>CCDiagError</name>
          <state/>
        </option>
        <option>
          <name>CCObjPrefix</name>
          <state>1</state>
        </option>
        <option>
          <name>CCAllowList</name>
          <version>1</version>
          <state>00100000</state>
        </option>
        <option>
          <name>CCDebugInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>IEndianMode</name>
          <state>1</state>
        </option>
        <option>
          <name>IProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>IExtraOptionsCheck</name>
          <state>1</state>
        </option>
        <option>
          <name>IExtraOptions</name>
          <state>--diag_suppress=Pa082,Pa050</state>
        </option>
        <option>
          <name>CCLangConformance</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSignedPlainChar</name>
          <state>1</state>
        </option>
        <option>
          <name>CCRequirePrototypes</name>
          <state>0</state>
        </option>
        <option>
          <name>CCDiagWarnAreErr</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCompilerRuntimeInfo</name>
          <state>0</state>
        </option>
        <option>
          <name>IFpuProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>OutputFile</name>
          <state>$FILE_BNAME$.o</state>
        </option>
        <option>
          <name>CCLibConfigHeader</name>
          <state>1</state>
        </option>
        <option>
          <name>PreInclude</name>
          <state>$PROJ_DIR$/../mcux_config.h</state>
        </option>
        <option>
          <name>CCIncludePath2</name>
          <state>$PROJ_DIR$/..</state>
          <state>$PROJ_DIR$/../../../../../../devices/MCXA153/drivers</state>
          <state>$PROJ_DIR$/../../../../../../CMSIS/Core/Include</state>
          <state>$PROJ_DIR$/../../../../../../CMSIS/Core/Include/m-profile</state>
          <state>$PROJ_DIR$/../../../../../../CMSIS/Driver/Include</state>
          <state>$PROJ_DIR$/../../../../../../CMSIS/Driver/Include/GPIO</state>
          <state>$PROJ_DIR$/../../../../../../devices/MCXA153</state>
          <state>$PROJ_DIR$/../../../../../../devices/MCXA153/periph</state>
          <state>$PROJ_DIR$/../../../../../../devices/MCXA153/cmsis_drivers</state>
          <state>$PROJ_DIR$/../../../../../../devices/MCXA153/utilities/debug_console_lite</state>
          <state>$PROJ_DIR$/../../../../../../devices/MCXA153/utilities/str</state>
          <state>$PROJ_DIR$/../../../../../../components/uart</state>
        </option>
        <option>
          <name>CCStdIncCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCodeSection</name>
          <state>.text</state>
        </option>
        <option>
          <name>IProcessorMode2</name>
          <state>1</state>
        </option>
        <option>
          <name>CCOptLevel</name>
          <state>0</state>
        </option>
        <option>
          <name>CCOptStrategy</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCOptLevelSlave</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPosIndRopi</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPosIndRwpi</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPosIndNoDynInit</name>
          <state>0</state>
        </option>
        <option>
          <name>IccLang</name>
          <state>0</state>
        </option>
        <option>
          <name>IccCDialect</name>
          <state>1</state>
        </option>
        <option>
          <name>IccAllowVLA</name>
          <state>0</state>
        </option>
        <option>
          <name>IccStaticDestr</name>
          <state>1</state>
        </option>
        <option>
          <name>IccCppInlineSemantics</name>
          <state>0</state>
        </option>
        <option>
          <name>IccCmsis</name>
          <state>1</state>
        </option>
        <option>
          <name>IccFloatSemantics</name>
          <state>0</state>
        </option>
        <option>
          <name>CCOptimizationNoSizeConstraints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCNoLiteralPool</name>
          <state>0</state>
        </option>
        <option>
          <name>CCOptStrategySlave</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCGuardCalls</name>
          <state>1</state>
        </option>
        <option>
          <name>CCEncSource</name>
          <state>0</state>
        </option>
        <option>
          <name>CCEncOutput</name>
          <state>0</state>
        </option>
        <option>
          <name>CCEncOutputBom</name>
          <state>1</state>
        </option>
        <option>
          <name>CCEncInput</name>
          <state>0</state>
        </option>
        <option>
          <name>IccExceptions2</name>
          <state>1</state>
        </option>
        <option>
          <name>IccRTTI2</name>
          <state>1</state>
        </option>
        <option>
          <name>OICompilerExtraOption</name>
          <state>1</state>
        </option>
        <option>
          <name>CCStackProtection</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPosRadRwpi</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPosSharedSlave</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>AARM</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>12</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>A_32_64Device</name>
          <state>1</state>
        </option>
        <option>
          <name>AObjPrefix</name>
          <state>1</state>
        </option>
        <option>
          <name>AEndian</name>
          <state>1</state>
        </option>
        <option>
          <name>ACaseSensitivity</name>
          <state>1</state>
        </option>
        <option>
          <name>MacroChars</name>
          <version>0</version>
          <state>3</state>
        </option>
        <option>
          <name>AWarnEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>AWarnWhat</name>
          <state>0</state>
        </option>
        <option>
          <name>AWarnOne</name>
          <state/>
        </option>
        <option>
          <name>AWarnRange1</name>
          <state/>
        </option>
        <option>
          <name>AWarnRange2</name>
          <state/>
        </option>
        <option>
          <name>ADebug</name>
          <state>1</state>
        </option>
        <option>
          <name>AltRegisterNames</name>
          <state>1</state>
        </option>
        <option>
          <name>ADefines</name>
          <state>MCUXPRESSO_SDK</state>
          <state>CPU_MCXA153VLH</state>
        </option>
        <option>
          <name>AList</name>
          <state>0</state>
        </option>
        <option>
          <name>AListHeader</name>
          <state>1</state>
        </option>
        <option>
          <name>AListing</name>
          <state>1</state>
        </option>
        <option>
          <name>Includes</name>
          <state>0</state>
        </option>
        <option>
          <name>MacDefs</name>
          <state>0</state>
        </option>
        <option>
          <name>MacExps</name>
          <state>1</state>
        </option>
        <option>
          <name>MacExec</name>
          <state>0</state>
        </option>
        <option>
          <name>OnlyAssed</name>
          <state>0</state>
        </option>
        <option>
          <name>MultiLine</name>
          <state>0</state>
        </option>
        <option>
          <name>PageLengthCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>PageLength</name>
          <state>80</state>
        </option>
        <option>
          <name>TabSpacing</name>
          <state>8</state>
        </option>
        <option>
          <name>AXRef</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefDefines</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefInternal</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefDual</name>
          <state>0</state>
        </option>
        <option>
          <name>AProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>AFpuProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>AOutputFile</name>
          <state>$FILE_BNAME$.o</state>
        </option>
        <option>
          <name>ALimitErrorsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>ALimitErrorsEdit</name>
          <state>100</state>
        </option>
        <option>
          <name>AIgnoreStdInclude</name>
          <state>0</state>
        </option>
        <option>
          <name>AUserIncludes</name>
          <state>$PROJ_DIR$/..</state>
          <state>$PROJ_DIR$/../../../../../../devices/MCXA153/drivers</state>
          <state>$PROJ_DIR$/../../../../../../CMSIS/Core/Include</state>
          <state>$PROJ_DIR$/../../../../../../CMSIS/Core/Include/m-profile</state>
          <state>$PROJ_DIR$/../../../../../../CMSIS/Driver/Include/GPIO</state>
          <state>$PROJ_DIR$/../../../../../../CMSIS/Driver/Include</state>
          <state>$PROJ_DIR$/../../../../../../devices/MCXA153</state>
          <state>$PROJ_DIR$/../../../../../../devices/MCXA153/periph</state>
          <state>$PROJ_DIR$/../../../../../../devices/MCXA153/cmsis_drivers</state>
          <state>$PROJ_DIR$/../../../../../../devices/MCXA153/utilities/debug_console_lite</state>
          <state>$PROJ_DIR$/../../../../../../devices/MCXA153/utilities/str</state>
          <state>$PROJ_DIR$/../../../../../../components/uart</state>
        </option>
        <option>
          <name>AExtraOptionsCheckV2</name>
          <state>0</state>
        </option>
        <option>
          <name>AExtraOptionsV2</name>
          <state/>
        </option>
        <option>
          <name>AsmNoLiteralPool</name>
          <state>0</state>
        </option>
        <option>
          <name>PreInclude</name>
          <state/>
        </option>
      </data>
    </settings>
    <settings>
      <name>OBJCOPY</name>
      <archiveVersion>0</archiveVersion>
      <data>
        <version>1</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OOCOutputFormat</name>
          <version>3</version>
          <state>3</state>
        </option>
        <option>
          <name>OCOutputOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>OOCOutputFile</name>
          <state/>
        </option>
        <option>
          <name>OOCCommandLineProducer</name>
          <state>1</state>
        </option>
        <option>
          <name>OOCObjCopyEnable</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>CUSTOM</name>
      <archiveVersion>4</archiveVersion>
      <data>
        <extensions/>
        <cmdline/>
        <buildSequence>inputOutputBased</buildSequence>
      </data>
    </settings>
    <settings>
      <name>ILINK</name>
      <archiveVersion>0</archiveVersion>
      <data>
        <version>28</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>IlinkLibIOConfig</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkInputFileSlave</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkOutputFile</name>
          <state>cmsis_button_toggle_led.out</state>
        </option>
        <option>
          <name>IlinkDebugInfoEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkKeepSymbols</name>
          <state/>
        </option>
        <option>
          <name>IlinkRawBinaryFile</name>
          <state/>
        </option>
        <option>
          <name>IlinkRawBinarySymbol</name>
          <state/>
        </option>
        <option>
          <name>IlinkRawBinarySegment</name>
          <state/>
        </option>
        <option>
          <name>IlinkRawBinaryAlign</name>
          <state/>
        </option>
        <option>
          <name>IlinkDefines</name>
          <state/>
        </option>
        <option>
          <name>IlinkConfigDefines</name>
        </option>
        <option>
          <name>IlinkMapFile</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkLogFile</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogInitialization</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogModule</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogSection</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogVeneer</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkIcfOverride</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkIcfFile</name>
          <state>$PROJ_DIR$/MCXA153_flash.icf</state>
        </option>
        <option>
          <name>IlinkIcfFileSlave</name>
          <state/>
        </option>
        <option>
          <name>IlinkEnableRemarks</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkSuppressDiags</name>
          <state/>
        </option>
        <option>
          <name>IlinkTreatAsRem</name>
          <state/>
        </option>
        <option>
          <name>IlinkTreatAsWarn</name>
          <state/>
        </option>
        <option>
          <name>IlinkTreatAsErr</name>
          <state/>
        </option>
        <option>
          <name>IlinkWarningsAreErrors</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkUseExtraOptions</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkExtraOptions</name>
        </option>
        <option>
          <name>IlinkLowLevelInterfaceSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkAutoLibEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkAdditionalLibs</name>
        </option>
        <option>
          <name>IlinkOverrideProgramEntryLabel</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkProgramEntryLabelSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkProgramEntryLabel</name>
          <state>Reset_Handler</state>
        </option>
        <option>
          <name>DoFill</name>
          <state>0</state>
        </option>
        <option>
          <name>FillerByte</name>
          <state>0xFF</state>
        </option>
        <option>
          <name>FillerStart</name>
          <state>0x0</state>
        </option>
        <option>
          <name>FillerEnd</name>
          <state>0x0</state>
        </option>
        <option>
          <name>CrcSize</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>CrcAlign</name>
          <state>1</state>
        </option>
        <option>
          <name>CrcPoly</name>
          <state>0x11021</state>
        </option>
        <option>
          <name>CrcCompl</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CrcBitOrder</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CrcInitialValue</name>
          <state>0x0</state>
        </option>
        <option>
          <name>DoCrc</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkBE8Slave</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkBufferedTerminalOutput</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkStdoutInterfaceSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>CrcFullSize</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkIElfToolPostProcess</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogAutoLibSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogRedirSymbols</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogUnusedFragments</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkCrcReverseByteOrder</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkCrcUseAsInput</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptInline</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkOptExceptionsAllow</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptExceptionsForce</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkCmsis</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptMergeDuplSections</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkOptUseVfe</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptForceVfe</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkStackAnalysisEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkStackControlFile</name>
          <state/>
        </option>
        <option>
          <name>IlinkStackCallGraphFile</name>
          <state/>
        </option>
        <option>
          <name>CrcAlgorithm</name>
          <version>1</version>
          <state>1</state>
        </option>
        <option>
          <name>CrcUnitSize</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>IlinkThreadsSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkLogCallGraph</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkIcfFile_AltDefault</name>
          <state/>
        </option>
        <option>
          <name>IlinkEncInput</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkEncOutput</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkEncOutputBom</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkHeapSelect</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkLocaleSelect</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkTrustzoneImportLibraryOut</name>
          <state>###Unitialized###</state>
        </option>
        <option>
          <name>OILinkExtraOption</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkRawBinaryFile2</name>
          <state/>
        </option>
        <option>
          <name>IlinkRawBinarySymbol2</name>
          <state/>
        </option>
        <option>
          <name>IlinkRawBinarySegment2</name>
          <state/>
        </option>
        <option>
          <name>IlinkRawBinaryAlign2</name>
          <state/>
        </option>
        <option>
          <name>IlinkLogCrtRoutineSelection</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogFragmentInfo</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogInlining</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogMerging</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkDemangle</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkWrapperFileEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkWrapperFile</name>
          <state/>
        </option>
        <option>
          <name>IlinkProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkFpuProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkSharedSlave</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>IARCHIVE</name>
      <archiveVersion>0</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>IarchiveInputs</name>
          <state/>
        </option>
        <option>
          <name>IarchiveOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>IarchiveOutput</name>
          <state>###Unitialized###</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>BUILDACTION</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <prebuild/>
        <postbuild/>
      </data>
    </settings>
  </configuration>
  <configuration>
    <name>release</name>
    <toolchain>
      <name>ARM</name>
    </toolchain>
    <debug>0</debug>
    <settings>
      <name>General</name>
      <archiveVersion>3</archiveVersion>
      <data>
        <version>37</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OG_32_64DeviceCoreSlave</name>
          <version>34</version>
          <state>38</state>
        </option>
        <option>
          <name>BrowseInfoPath</name>
          <state>Release\BrowseInfo</state>
        </option>
        <option>
          <name>ExePath</name>
          <state>$PROJ_DIR$/release</state>
        </option>
        <option>
          <name>ObjPath</name>
          <state>$PROJ_DIR$/release/obj</state>
        </option>
        <option>
          <name>ListPath</name>
          <state>$PROJ_DIR$/release/list</state>
        </option>
        <option>
          <name>GEndianMode</name>
          <state>0</state>
        </option>
        <option>
          <name>Input description</name>
          <state>No specifier n, no float nor long long, no scan set, no assignment suppressing, without multibyte support.</state>
        </option>
        <option>
          <name>Output description</name>
          <state>No specifier a, A, no specifier n, no float nor long long, without multibyte support.</state>
        </option>
        <option>
          <name>GOutputBinary</name>
          <state>0</state>
        </option>
        <option>
          <name>OGCoreOrChip</name>
          <state>1</state>
        </option>
        <option>
          <name>GRuntimeLibSelect</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>GRuntimeLibSelectSlave</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>RTDescription</name>
          <state>0</state>
        </option>
        <option>
          <name>OGProductVersion</name>
          <state>6.50.6.4952</state>
        </option>
        <option>
          <name>OGLastSavedByProductVersion</name>
          <state>8.30.2.18207</state>
        </option>
        <option>
          <name>OGChipSelectEditMenu</name>
          <state>MCXA153	NXP MCXA153</state>
        </option>
        <option>
          <name>GenLowLevelInterface</name>
          <state>0</state>
        </option>
        <option>
          <name>GEndianModeBE</name>
          <state>1</state>
        </option>
        <option>
          <name>OGBufferedTerminalOutput</name>
          <state>0</state>
        </option>
        <option>
          <name>GenStdoutInterface</name>
          <state>0</state>
        </option>
        <option>
          <name>RTConfigPath2</name>
          <state>$TOOLKIT_DIR$\inc\c\DLib_Config_Normal.h</state>
        </option>
        <option>
          <name>GBECoreSlave</name>
          <version>34</version>
          <state>58</state>
        </option>
        <option>
          <name>OGUseCmsis</name>
          <state>0</state>
        </option>
        <option>
          <name>OGUseCmsisDspLib</name>
          <state>0</state>
        </option>
        <option>
          <name>GRuntimeLibThreads</name>
          <state>0</state>
        </option>
        <option>
          <name>CoreVariant</name>
          <version>34</version>
          <state>58</state>
        </option>
        <option>
          <name>GFPUDeviceSlave</name>
          <state>MCXA153	NXP MCXA153</state>
        </option>
        <option>
          <name>FPU2</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>NrRegs</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>NEON</name>
          <state>0</state>
        </option>
        <option>
          <name>GFPUCoreSlave2</name>
          <version>34</version>
          <state>58</state>
        </option>
        <option>
          <name>OGCMSISPackSelectDevice</name>
        </option>
        <option>
          <name>OgLibHeap</name>
          <state>0</state>
        </option>
        <option>
          <name>OGLibAdditionalLocale</name>
          <state>0</state>
        </option>
        <option>
          <name>OGPrintfVariant</name>
          <version>0</version>
          <state>3</state>
        </option>
        <option>
          <name>OGPrintfMultibyteSupport</name>
          <state>0</state>
        </option>
        <option>
          <name>OGScanfVariant</name>
          <version>0</version>
          <state>3</state>
        </option>
        <option>
          <name>OGScanfMultibyteSupport</name>
          <state>0</state>
        </option>
        <option>
          <name>GenLocaleTags</name>
          <state/>
        </option>
        <option>
          <name>GenLocaleDisplayOnly</name>
          <state/>
        </option>
        <option>
          <name>DSPExtension</name>
          <state>0</state>
        </option>
        <option>
          <name>TrustZone</name>
          <state>0</state>
        </option>
        <option>
          <name>TrustZoneModes</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>OGAarch64Abi</name>
          <state>0</state>
        </option>
        <option>
          <name>OG_32_64Device</name>
          <state>0</state>
        </option>
        <option>
          <name>BuildFilesPath</name>
          <state>Release</state>
        </option>
        <option>
          <name>PointerAuthentication</name>
          <state>0</state>
        </option>
        <option>
          <name>FPU64</name>
          <state>1</state>
        </option>
        <option>
          <name>GOutputSo</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>ICCARM</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>39</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>CCPointerAutentiction</name>
          <state>0</state>
        </option>
        <option>
          <name>CCBranchTargetIdentification</name>
          <state>0</state>
        </option>
        <option>
          <name>CCDefines</name>
          <state>NDEBUG</state>
          <state>SDK_DEBUGCONSOLE=1</state>
          <state>MCUX_META_BUILD</state>
          <state>MCUXPRESSO_SDK</state>
          <state>CPU_MCXA153VLH</state>
        </option>
        <option>
          <name>CCPreprocFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPreprocComments</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPreprocLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListCFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListCMnemonics</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListCMessages</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListAssFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListAssSource</name>
          <state>0</state>
        </option>
        <option>
          <name>CCEnableRemarks</name>
          <state>0</state>
        </option>
        <option>
          <name>CCDiagSuppress</name>
          <state/>
        </option>
        <option>
          <name>CCDiagRemark</name>
          <state/>
        </option>
        <option>
          <name>CCDiagWarning</name>
          <state/>
        </option>
        <option>
          <name>CCDiagError</name>
          <state/>
        </option>
        <option>
          <name>CCObjPrefix</name>
          <state>1</state>
        </option>
        <option>
          <name>CCAllowList</name>
          <version>1</version>
          <state>11111110</state>
        </option>
        <option>
          <name>CCDebugInfo</name>
          <state>0</state>
        </option>
        <option>
          <name>IEndianMode</name>
          <state>1</state>
        </option>
        <option>
          <name>IProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>IExtraOptionsCheck</name>
          <state>1</state>
        </option>
        <option>
          <name>IExtraOptions</name>
          <state>--diag_suppress=Pa082,Pa050</state>
        </option>
        <option>
          <name>CCLangConformance</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSignedPlainChar</name>
          <state>1</state>
        </option>
        <option>
          <name>CCRequirePrototypes</name>
          <state>0</state>
        </option>
        <option>
          <name>CCDiagWarnAreErr</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCompilerRuntimeInfo</name>
          <state>0</state>
        </option>
        <option>
          <name>IFpuProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>OutputFile</name>
          <state>$FILE_BNAME$.o</state>
        </option>
        <option>
          <name>CCLibConfigHeader</name>
          <state>1</state>
        </option>
        <option>
          <name>PreInclude</name>
          <state>$PROJ_DIR$/../mcux_config.h</state>
        </option>
        <option>
          <name>CCIncludePath2</name>
          <state>$PROJ_DIR$/..</state>
          <state>$PROJ_DIR$/../../../../../../devices/MCXA153/drivers</state>
          <state>$PROJ_DIR$/../../../../../../CMSIS/Core/Include</state>
          <state>$PROJ_DIR$/../../../../../../CMSIS/Core/Include/m-profile</state>
          <state>$PROJ_DIR$/../../../../../../CMSIS/Driver/Include</state>
          <state>$PROJ_DIR$/../../../../../../CMSIS/Driver/Include/GPIO</state>
          <state>$PROJ_DIR$/../../../../../../devices/MCXA153</state>
          <state>$PROJ_DIR$/../../../../../../devices/MCXA153/periph</state>
          <state>$PROJ_DIR$/../../../../../../devices/MCXA153/cmsis_drivers</state>
          <state>$PROJ_DIR$/../../../../../../devices/MCXA153/utilities/debug_console_lite</state>
          <state>$PROJ_DIR$/../../../../../../devices/MCXA153/utilities/str</state>
          <state>$PROJ_DIR$/../../../../../../components/uart</state>
        </option>
        <option>
          <name>CCStdIncCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCodeSection</name>
          <state>.text</state>
        </option>
        <option>
          <name>IProcessorMode2</name>
          <state>1</state>
        </option>
        <option>
          <name>CCOptLevel</name>
          <state>3</state>
        </option>
        <option>
          <name>CCOptStrategy</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCOptLevelSlave</name>
          <state>3</state>
        </option>
        <option>
          <name>CCPosIndRopi</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPosIndRwpi</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPosIndNoDynInit</name>
          <state>0</state>
        </option>
        <option>
          <name>IccLang</name>
          <state>0</state>
        </option>
        <option>
          <name>IccCDialect</name>
          <state>1</state>
        </option>
        <option>
          <name>IccAllowVLA</name>
          <state>0</state>
        </option>
        <option>
          <name>IccStaticDestr</name>
          <state>1</state>
        </option>
        <option>
          <name>IccCppInlineSemantics</name>
          <state>0</state>
        </option>
        <option>
          <name>IccCmsis</name>
          <state>1</state>
        </option>
        <option>
          <name>IccFloatSemantics</name>
          <state>0</state>
        </option>
        <option>
          <name>CCOptimizationNoSizeConstraints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCNoLiteralPool</name>
          <state>0</state>
        </option>
        <option>
          <name>CCOptStrategySlave</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CCGuardCalls</name>
          <state>1</state>
        </option>
        <option>
          <name>CCEncSource</name>
          <state>0</state>
        </option>
        <option>
          <name>CCEncOutput</name>
          <state>0</state>
        </option>
        <option>
          <name>CCEncOutputBom</name>
          <state>1</state>
        </option>
        <option>
          <name>CCEncInput</name>
          <state>0</state>
        </option>
        <option>
          <name>IccExceptions2</name>
          <state>1</state>
        </option>
        <option>
          <name>IccRTTI2</name>
          <state>1</state>
        </option>
        <option>
          <name>OICompilerExtraOption</name>
          <state>1</state>
        </option>
        <option>
          <name>CCStackProtection</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPosRadRwpi</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPosSharedSlave</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>AARM</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>12</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>A_32_64Device</name>
          <state>1</state>
        </option>
        <option>
          <name>AObjPrefix</name>
          <state>1</state>
        </option>
        <option>
          <name>AEndian</name>
          <state>1</state>
        </option>
        <option>
          <name>ACaseSensitivity</name>
          <state>1</state>
        </option>
        <option>
          <name>MacroChars</name>
          <version>0</version>
          <state>3</state>
        </option>
        <option>
          <name>AWarnEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>AWarnWhat</name>
          <state>0</state>
        </option>
        <option>
          <name>AWarnOne</name>
          <state/>
        </option>
        <option>
          <name>AWarnRange1</name>
          <state/>
        </option>
        <option>
          <name>AWarnRange2</name>
          <state/>
        </option>
        <option>
          <name>ADebug</name>
          <state>0</state>
        </option>
        <option>
          <name>AltRegisterNames</name>
          <state>1</state>
        </option>
        <option>
          <name>ADefines</name>
          <state>MCUXPRESSO_SDK</state>
          <state>CPU_MCXA153VLH</state>
        </option>
        <option>
          <name>AList</name>
          <state>0</state>
        </option>
        <option>
          <name>AListHeader</name>
          <state>1</state>
        </option>
        <option>
          <name>AListing</name>
          <state>1</state>
        </option>
        <option>
          <name>Includes</name>
          <state>0</state>
        </option>
        <option>
          <name>MacDefs</name>
          <state>0</state>
        </option>
        <option>
          <name>MacExps</name>
          <state>1</state>
        </option>
        <option>
          <name>MacExec</name>
          <state>0</state>
        </option>
        <option>
          <name>OnlyAssed</name>
          <state>0</state>
        </option>
        <option>
          <name>MultiLine</name>
          <state>0</state>
        </option>
        <option>
          <name>PageLengthCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>PageLength</name>
          <state>80</state>
        </option>
        <option>
          <name>TabSpacing</name>
          <state>8</state>
        </option>
        <option>
          <name>AXRef</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefDefines</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefInternal</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefDual</name>
          <state>0</state>
        </option>
        <option>
          <name>AProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>AFpuProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>AOutputFile</name>
          <state>$FILE_BNAME$.o</state>
        </option>
        <option>
          <name>ALimitErrorsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>ALimitErrorsEdit</name>
          <state>100</state>
        </option>
        <option>
          <name>AIgnoreStdInclude</name>
          <state>0</state>
        </option>
        <option>
          <name>AUserIncludes</name>
          <state>$PROJ_DIR$/..</state>
          <state>$PROJ_DIR$/../../../../../../devices/MCXA153/drivers</state>
          <state>$PROJ_DIR$/../../../../../../CMSIS/Core/Include</state>
          <state>$PROJ_DIR$/../../../../../../CMSIS/Core/Include/m-profile</state>
          <state>$PROJ_DIR$/../../../../../../CMSIS/Driver/Include/GPIO</state>
          <state>$PROJ_DIR$/../../../../../../CMSIS/Driver/Include</state>
          <state>$PROJ_DIR$/../../../../../../devices/MCXA153</state>
          <state>$PROJ_DIR$/../../../../../../devices/MCXA153/periph</state>
          <state>$PROJ_DIR$/../../../../../../devices/MCXA153/cmsis_drivers</state>
          <state>$PROJ_DIR$/../../../../../../devices/MCXA153/utilities/debug_console_lite</state>
          <state>$PROJ_DIR$/../../../../../../devices/MCXA153/utilities/str</state>
          <state>$PROJ_DIR$/../../../../../../components/uart</state>
        </option>
        <option>
          <name>AExtraOptionsCheckV2</name>
          <state>0</state>
        </option>
        <option>
          <name>AExtraOptionsV2</name>
          <state/>
        </option>
        <option>
          <name>AsmNoLiteralPool</name>
          <state>0</state>
        </option>
        <option>
          <name>PreInclude</name>
          <state/>
        </option>
      </data>
    </settings>
    <settings>
      <name>OBJCOPY</name>
      <archiveVersion>0</archiveVersion>
      <data>
        <version>1</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>OOCOutputFormat</name>
          <version>3</version>
          <state>3</state>
        </option>
        <option>
          <name>OCOutputOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>OOCOutputFile</name>
          <state/>
        </option>
        <option>
          <name>OOCCommandLineProducer</name>
          <state>1</state>
        </option>
        <option>
          <name>OOCObjCopyEnable</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>CUSTOM</name>
      <archiveVersion>4</archiveVersion>
      <data>
        <extensions/>
        <cmdline/>
        <buildSequence>inputOutputBased</buildSequence>
      </data>
    </settings>
    <settings>
      <name>ILINK</name>
      <archiveVersion>0</archiveVersion>
      <data>
        <version>28</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>IlinkLibIOConfig</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkInputFileSlave</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkOutputFile</name>
          <state>cmsis_button_toggle_led.out</state>
        </option>
        <option>
          <name>IlinkDebugInfoEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkKeepSymbols</name>
          <state/>
        </option>
        <option>
          <name>IlinkRawBinaryFile</name>
          <state/>
        </option>
        <option>
          <name>IlinkRawBinarySymbol</name>
          <state/>
        </option>
        <option>
          <name>IlinkRawBinarySegment</name>
          <state/>
        </option>
        <option>
          <name>IlinkRawBinaryAlign</name>
          <state/>
        </option>
        <option>
          <name>IlinkDefines</name>
          <state/>
        </option>
        <option>
          <name>IlinkConfigDefines</name>
        </option>
        <option>
          <name>IlinkMapFile</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkLogFile</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogInitialization</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogModule</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogSection</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogVeneer</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkIcfOverride</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkIcfFile</name>
          <state>$PROJ_DIR$/MCXA153_flash.icf</state>
        </option>
        <option>
          <name>IlinkIcfFileSlave</name>
          <state/>
        </option>
        <option>
          <name>IlinkEnableRemarks</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkSuppressDiags</name>
          <state/>
        </option>
        <option>
          <name>IlinkTreatAsRem</name>
          <state/>
        </option>
        <option>
          <name>IlinkTreatAsWarn</name>
          <state/>
        </option>
        <option>
          <name>IlinkTreatAsErr</name>
          <state/>
        </option>
        <option>
          <name>IlinkWarningsAreErrors</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkUseExtraOptions</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkExtraOptions</name>
        </option>
        <option>
          <name>IlinkLowLevelInterfaceSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkAutoLibEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkAdditionalLibs</name>
        </option>
        <option>
          <name>IlinkOverrideProgramEntryLabel</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkProgramEntryLabelSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkProgramEntryLabel</name>
          <state>Reset_Handler</state>
        </option>
        <option>
          <name>DoFill</name>
          <state>0</state>
        </option>
        <option>
          <name>FillerByte</name>
          <state>0xFF</state>
        </option>
        <option>
          <name>FillerStart</name>
          <state>0x0</state>
        </option>
        <option>
          <name>FillerEnd</name>
          <state>0x0</state>
        </option>
        <option>
          <name>CrcSize</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>CrcAlign</name>
          <state>1</state>
        </option>
        <option>
          <name>CrcPoly</name>
          <state>0x11021</state>
        </option>
        <option>
          <name>CrcCompl</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CrcBitOrder</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CrcInitialValue</name>
          <state>0x0</state>
        </option>
        <option>
          <name>DoCrc</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkBE8Slave</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkBufferedTerminalOutput</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkStdoutInterfaceSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>CrcFullSize</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkIElfToolPostProcess</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogAutoLibSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogRedirSymbols</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogUnusedFragments</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkCrcReverseByteOrder</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkCrcUseAsInput</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptInline</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptExceptionsAllow</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptExceptionsForce</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkCmsis</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptMergeDuplSections</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkOptUseVfe</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptForceVfe</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkStackAnalysisEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkStackControlFile</name>
          <state/>
        </option>
        <option>
          <name>IlinkStackCallGraphFile</name>
          <state/>
        </option>
        <option>
          <name>CrcAlgorithm</name>
          <version>1</version>
          <state>1</state>
        </option>
        <option>
          <name>CrcUnitSize</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>IlinkThreadsSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkLogCallGraph</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkIcfFile_AltDefault</name>
          <state/>
        </option>
        <option>
          <name>IlinkEncInput</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkEncOutput</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkEncOutputBom</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkHeapSelect</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkLocaleSelect</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkTrustzoneImportLibraryOut</name>
          <state>###Unitialized###</state>
        </option>
        <option>
          <name>OILinkExtraOption</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkRawBinaryFile2</name>
          <state/>
        </option>
        <option>
          <name>IlinkRawBinarySymbol2</name>
          <state/>
        </option>
        <option>
          <name>IlinkRawBinarySegment2</name>
          <state/>
        </option>
        <option>
          <name>IlinkRawBinaryAlign2</name>
          <state/>
        </option>
        <option>
          <name>IlinkLogCrtRoutineSelection</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogFragmentInfo</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogInlining</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogMerging</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkDemangle</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkWrapperFileEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkWrapperFile</name>
          <state/>
        </option>
        <option>
          <name>IlinkProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkFpuProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkSharedSlave</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>IARCHIVE</name>
      <archiveVersion>0</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>0</debug>
        <option>
          <name>IarchiveInputs</name>
          <state/>
        </option>
        <option>
          <name>IarchiveOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>IarchiveOutput</name>
          <state/>
        </option>
      </data>
    </settings>
    <settings>
      <name>BUILDACTION</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <prebuild/>
        <postbuild/>
      </data>
    </settings>
  </configuration>
  <group>
    <name>source</name>
    <file>
      <name>$PROJ_DIR$/../cmsis_button_toggle_led.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../RTE_Device.h</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../mcux_config.h</name>
    </file>
  </group>
  <group>
    <name>board</name>
    <file>
      <name>$PROJ_DIR$/../board.h</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../board.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../clock_config.h</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../clock_config.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../pin_mux.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../pin_mux.h</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../hardware_init.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../app.h</name>
    </file>
  </group>
  <group>
    <name>drivers</name>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/drivers/fsl_clock.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/drivers/fsl_clock.h</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/drivers/fsl_edma_soc.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/drivers/fsl_edma_soc.h</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/drivers/fsl_inputmux_connections.h</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/drivers/fsl_reset.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/drivers/fsl_reset.h</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/drivers/fsl_common.h</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/drivers/fsl_common.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/drivers/fsl_common_arm.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/drivers/fsl_common_arm.h</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/drivers/fsl_edma.h</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/drivers/fsl_edma_core.h</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/drivers/fsl_edma.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/drivers/fsl_gpio.h</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/drivers/fsl_gpio.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/drivers/fsl_inputmux.h</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/drivers/fsl_inputmux.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/drivers/fsl_lpuart.h</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/drivers/fsl_lpuart.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/drivers/fsl_spc.h</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/drivers/fsl_spc.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/drivers/fsl_port.h</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/cmsis_drivers/fsl_gpio_cmsis.h</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/cmsis_drivers/fsl_gpio_cmsis.c</name>
    </file>
  </group>
  <group>
    <name>CMSIS</name>
    <file>
      <name>$PROJ_DIR$/../../../../../../CMSIS/Core/Include/cmsis_compiler.h</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../../../../../../CMSIS/Core/Include/cmsis_version.h</name>
    </file>
    <group>
      <name>m-profile</name>
      <file>
        <name>$PROJ_DIR$/../../../../../../CMSIS/Core/Include/m-profile/cmsis_iccarm_m.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../CMSIS/Core/Include/m-profile/armv8m_mpu.h</name>
      </file>
    </group>
    <file>
      <name>$PROJ_DIR$/../../../../../../CMSIS/Core/Include/core_cm33.h</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../../../../../../CMSIS/Core/Include/tz_context.h</name>
    </file>
  </group>
  <group>
    <name>CMSIS_driver</name>
    <group>
      <name>Include</name>
      <file>
        <name>$PROJ_DIR$/../../../../../../CMSIS/Driver/Include/Driver_Common.h</name>
      </file>
      <group>
        <name>GPIO</name>
        <file>
          <name>$PROJ_DIR$/../../../../../../CMSIS/Driver/Include/GPIO/Driver_GPIO.h</name>
        </file>
      </group>
    </group>
  </group>
  <group>
    <name>device</name>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/fsl_device_registers.h</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/MCXA153.h</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/MCXA153_features.h</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/MCXA153_COMMON.h</name>
    </file>
    <group>
      <name>periph</name>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_ADC.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_AOI.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_CDOG.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_CMC.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_CRC.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_CTIMER.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_DEBUGMAILBOX.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_DMA.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_EIM.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_EQDC.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_ERM.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_FMC.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_FMU.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_FMUTEST.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_FREQME.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_GLIKEY.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_GPIO.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_I3C.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_INPUTMUX.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_LPCMP.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_LPI2C.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_LPSPI.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_LPTMR.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_LPUART.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_MRCC.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_OSTIMER.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_PORT.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_PWM.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_SCG.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_SPC.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_SYSCON.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_TRDC.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_USB.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_UTICK.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_VBAT.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_WAKETIMER.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_WUU.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/periph/PERI_WWDT.h</name>
      </file>
    </group>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/system_MCXA153.c</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/system_MCXA153.h</name>
    </file>
  </group>
  <group>
    <name>startup</name>
    <group>
      <name>iar</name>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/iar/startup_MCXA153.s</name>
      </file>
    </group>
  </group>
  <group>
    <name>utilities</name>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/utilities/debug_console_lite/fsl_assert.h</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../../../../../../devices/MCXA153/utilities/debug_console_lite/fsl_assert.c</name>
    </file>
    <group>
      <name>str</name>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/utilities/str/fsl_str.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/utilities/str/fsl_str.c</name>
      </file>
    </group>
    <group>
      <name>debug_console_lite</name>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/utilities/debug_console_lite/fsl_debug_console.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../devices/MCXA153/utilities/debug_console_lite/fsl_debug_console.c</name>
      </file>
    </group>
  </group>
  <group>
    <name>component</name>
    <group>
      <name>uart</name>
      <file>
        <name>$PROJ_DIR$/../../../../../../components/uart/fsl_adapter_uart.h</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../../../../../../components/uart/fsl_adapter_lpuart.c</name>
      </file>
    </group>
  </group>
  <group>
    <name>doc</name>
    <file>
      <name>$PROJ_DIR$/../readme.md</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../example_board_readme.md</name>
    </file>
    <file>
      <name>$PROJ_DIR$/../examples_shared_readme.md</name>
    </file>
  </group>
</project>
