<?xml version="1.0" encoding="UTF-8"?>
<ksdk:examples xmlns:ksdk="http://nxp.com/ksdk/2.0/ksdk_manifest_v3.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://nxp.com/ksdk/2.0/ksdk_manifest_v3.0.xsd manifest.xsd">
  <externalDefinitions>
    <definition extID="cm33_core0_MCXA153"/>
    <definition extID="platform.drivers.clock.MCXA153"/>
    <definition extID="platform.drivers.edma_soc.MCXA153"/>
    <definition extID="platform.drivers.inputmux_connections.MCXA153"/>
    <definition extID="platform.drivers.reset.MCXA153"/>
    <definition extID="CMSIS_Include_core_cm.MCXA153"/>
    <definition extID="CMSIS_Driver_Include.SPI.MCXA153"/>
    <definition extID="device.MCXA153_CMSIS.MCXA153"/>
    <definition extID="device.MCXA153_system.MCXA153"/>
    <definition extID="device.MCXA153_startup.MCXA153"/>
    <definition extID="RTE_Device.MCXA153"/>
    <definition extID="platform.drivers.common.MCXA153"/>
    <definition extID="platform.drivers.edma4.MCXA153"/>
    <definition extID="platform.drivers.gpio.MCXA153"/>
    <definition extID="platform.drivers.lpspi_edma.MCXA153"/>
    <definition extID="platform.drivers.lpspi.MCXA153"/>
    <definition extID="platform.drivers.lpuart.MCXA153"/>
    <definition extID="platform.drivers.mcx_spc.MCXA153"/>
    <definition extID="platform.drivers.port.MCXA153"/>
    <definition extID="platform.drivers.lpspi_cmsis.MCXA153"/>
    <definition extID="platform.utilities.assert_lite.MCXA153"/>
    <definition extID="platform.utilities.misc_utilities.MCXA153"/>
    <definition extID="utility.str.MCXA153"/>
    <definition extID="utility.debug_console_lite.MCXA153"/>
    <definition extID="component.lpuart_adapter.MCXA153"/>
    <definition extID="iar"/>
    <definition extID="mdk"/>
    <definition extID="armgcc"/>
    <definition extID="mcuxpresso"/>
    <definition extID="com.nxp.mcuxpresso"/>
  </externalDefinitions>
  <example id="frdmmcxa153_cmsis_lpspi_edma_b2b_transfer_slave" name="cmsis_lpspi_edma_b2b_transfer_slave" device_core="cm33_core0_MCXA153" dependency="platform.drivers.clock.MCXA153 platform.drivers.edma_soc.MCXA153 platform.drivers.inputmux_connections.MCXA153 platform.drivers.reset.MCXA153 CMSIS_Include_core_cm.MCXA153 CMSIS_Driver_Include.SPI.MCXA153 device.MCXA153_CMSIS.MCXA153 device.MCXA153_system.MCXA153 device.MCXA153_startup.MCXA153 RTE_Device.MCXA153 platform.drivers.common.MCXA153 platform.drivers.edma4.MCXA153 platform.drivers.gpio.MCXA153 platform.drivers.lpspi_edma.MCXA153 platform.drivers.lpspi.MCXA153 platform.drivers.lpuart.MCXA153 platform.drivers.mcx_spc.MCXA153 platform.drivers.port.MCXA153 platform.drivers.lpspi_cmsis.MCXA153 platform.utilities.assert_lite.MCXA153 platform.utilities.misc_utilities.MCXA153 utility.str.MCXA153 utility.debug_console_lite.MCXA153 component.lpuart_adapter.MCXA153" category="cmsis_driver_examples/lpspi">
    <projects>
      <project type="com.crt.advproject.projecttype.exe" nature="org.eclipse.cdt.core.cnature"/>
    </projects>
    <toolchainSettings>
      <toolchainSetting id_refs="com.nxp.mcuxpresso">
        <option id="gnu.c.compiler.option.preprocessor.def.symbols" type="stringList">
          <value>MCUXPRESSO_SDK</value>
          <value>CPU_MCXA153VLH</value>
          <value>SDK_DEBUGCONSOLE=1</value>
          <value>MCUX_META_BUILD</value>
        </option>
        <option id="com.crt.advproject.gas.fpu" type="enum">
          <value>com.crt.advproject.gas.fpu.none</value>
        </option>
        <option id="gnu.c.compiler.option.optimization.flags" type="string">
          <value>-fno-common</value>
        </option>
        <option id="com.crt.advproject.c.misc.dialect" type="enum">
          <value>com.crt.advproject.misc.dialect.gnu99</value>
        </option>
        <option id="com.crt.advproject.gcc.fpu" type="enum">
          <value>com.crt.advproject.gcc.fpu.none</value>
        </option>
        <option id="gnu.c.compiler.option.misc.other" type="string">
          <value>-mcpu=cortex-m33+nodsp -c -ffunction-sections -fdata-sections -fno-builtin</value>
        </option>
        <option id="gnu.c.compiler.option.warnings.allwarn" type="boolean">
          <value>false</value>
        </option>
        <option id="gnu.c.compiler.option.warnings.toerrors" type="boolean">
          <value>false</value>
        </option>
        <option id="gnu.c.link.option.nostdlibs" type="boolean">
          <value>true</value>
        </option>
        <option id="gnu.c.link.option.other" type="stringList">
          <value>-no-warn-rwx-segments</value>
        </option>
        <option id="com.crt.advproject.link.fpu" type="enum">
          <value>com.crt.advproject.link.fpu.none</value>
        </option>
        <option id="gnu.c.link.option.group" type="boolean">
          <value>true</value>
        </option>
      </toolchainSetting>
    </toolchainSettings>
    <include_paths>
      <include_path path="." project_relative_path="source" type="c_include"/>
      <include_path path="." project_relative_path="board" type="c_include"/>
      <include_path path="." project_relative_path="source" type="asm_include"/>
    </include_paths>
    <source path="iar" project_relative_path="./" type="workspace" toolchain="iar">
      <files mask="cmsis_lpspi_edma_b2b_transfer_slave.ewp"/>
      <files mask="cmsis_lpspi_edma_b2b_transfer_slave.ewd"/>
      <files mask="cmsis_lpspi_edma_b2b_transfer_slave.eww"/>
    </source>
    <source path="mdk" project_relative_path="./" type="workspace" toolchain="mdk">
      <files mask="cmsis_lpspi_edma_b2b_transfer_slave.uvprojx"/>
      <files mask="cmsis_lpspi_edma_b2b_transfer_slave.uvoptx"/>
      <files mask="JLinkSettings.JLinkScript"/>
      <files mask="cmsis_lpspi_edma_b2b_transfer_slave.uvmpw"/>
    </source>
    <source path="armgcc" project_relative_path="./" type="workspace" toolchain="armgcc">
      <files mask="build_all.bat"/>
      <files mask="build_all.sh"/>
      <files mask="clean.bat"/>
      <files mask="clean.sh"/>
      <files mask="CMakeLists.txt"/>
      <files mask="flags.cmake"/>
      <files mask="config.cmake"/>
      <files mask="build_debug.bat"/>
      <files mask="build_debug.sh"/>
      <files mask="build_release.bat"/>
      <files mask="build_release.sh"/>
    </source>
    <source path="../../../../../../devices/MCXA153" project_relative_path="./" type="workspace" toolchain="armgcc">
      <files mask="all_lib_device.cmake"/>
    </source>
    <source path="." project_relative_path="source" type="src">
      <files mask="cmsis_lpspi_edma_b2b_transfer_slave.c"/>
    </source>
    <source path="." project_relative_path="source" type="c_include" config="true">
      <files mask="RTE_Device.h"/>
    </source>
    <source path="." project_relative_path="board" type="c_include">
      <files mask="board.h"/>
      <files mask="clock_config.h"/>
    </source>
    <source path="." project_relative_path="board" type="src">
      <files mask="board.c"/>
      <files mask="clock_config.c"/>
    </source>
    <source path="iar" project_relative_path="MCXA153/iar" type="linker" toolchain="iar">
      <files mask="MCXA153_flash.icf"/>
    </source>
    <source path="armgcc" project_relative_path="MCXA153/armgcc" type="linker" toolchain="armgcc">
      <files mask="MCXA153_flash.ld"/>
    </source>
    <source path="mdk" project_relative_path="MCXA153/mdk" type="linker" toolchain="mdk">
      <files mask="MCXA153_flash.scf"/>
    </source>
    <source path="." project_relative_path="board" type="src">
      <files mask="pin_mux.c"/>
      <files mask="hardware_init.c"/>
    </source>
    <source path="." project_relative_path="board" type="c_include">
      <files mask="pin_mux.h"/>
      <files mask="app.h"/>
    </source>
    <source path="." project_relative_path="source" type="c_preinclude">
      <files mask="mcux_config.h"/>
    </source>
    <source path="." project_relative_path="doc" type="doc">
      <files mask="readme.md"/>
    </source>
    <source path="." project_relative_path="doc" type="doc">
      <files mask="example_board_readme.md"/>
    </source>
    <source path="." project_relative_path="doc" type="doc">
      <files mask="examples_shared_readme.md"/>
    </source>
  </example>
</ksdk:examples>
