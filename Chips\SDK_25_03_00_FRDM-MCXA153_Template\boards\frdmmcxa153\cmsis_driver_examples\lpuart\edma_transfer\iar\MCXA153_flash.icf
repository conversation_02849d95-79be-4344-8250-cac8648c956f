/*
** ###################################################################
**     Processors:          MCXA153VFM
**                          MCXA153VFT
**                          MCXA153VLF
**                          MCXA153VLH
**
**     Compiler:            IAR ANSI C/C++ Compiler for ARM
**     Reference manual:    MCXA1 User manual
**     Version:             rev. 1.0, 2022-03-29
**     Build:               b240704
**
**     Abstract:
**         Linker file for the IAR ANSI C/C++ Compiler for ARM
**
**     Copyright 2016 Freescale Semiconductor, Inc.
**     Copyright 2016-2024 NXP
**     SPDX-License-Identifier: BSD-3-Clause
**
**     http:                 www.nxp.com
**     mail:                 <EMAIL>
**
** ###################################################################
*/


/* <PERSON><PERSON> and <PERSON><PERSON><PERSON> */
if (isdefinedsymbol(__stack_size__)) {
  define symbol __size_cstack__        = __stack_size__;
} else {
  define symbol __size_cstack__        = 0x0400;
}

if (isdefinedsymbol(__heap_size__)) {
  define symbol __size_heap__          = __heap_size__;
} else {
  define symbol __size_heap__          = 0x0400;
}

define symbol m_interrupts_start               = 0x00000000;
define symbol m_interrupts_end                 = 0x000001FF;

define symbol m_text_start                     = 0x00000200;
define symbol m_text_end                       = 0x0001FFFF;

define symbol m_data_start                     = 0x20000000;
define symbol m_data_end                       = 0x20005FFF;

define symbol m_sramx0_start                   = 0x04000000;
define symbol m_sramx0_end                     = 0x04001FFF;

define memory mem with size = 4G;

define region TEXT_region             = mem:[from m_interrupts_start to m_interrupts_end]
                                      | mem:[from m_text_start to m_text_end];
define region DATA_region = mem:[from m_data_start to m_data_end-__size_cstack__]
                          | mem:[from m_sramx0_start to m_sramx0_end];

define region CSTACK_region = mem:[from m_data_end-__size_cstack__+1 to m_data_end];
if (isdefinedsymbol(__use_shmem__)) {
  define region rpmsg_sh_mem_region     = mem:[from rpmsg_sh_mem_start to rpmsg_sh_mem_end];
}

define block CSTACK    with alignment = 8, size = __size_cstack__   { };
define block HEAP      with alignment = 8, size = __size_heap__     { };
define block RW        { readwrite };
define block ZI        { zi };

initialize by copy { readwrite, section .textrw };

if (isdefinedsymbol(__USE_DLIB_PERTHREAD))
{
  /* Required in a multi-threaded application */
  initialize by copy with packing = none { section __DLIB_PERTHREAD };
}


place at address mem: m_interrupts_start    { readonly section .intvec };
place in TEXT_region                        { readonly };
place in DATA_region                        { block RW };
place in DATA_region                        { block ZI };
place in DATA_region                        { last block HEAP };
place in CSTACK_region                      { block CSTACK };



