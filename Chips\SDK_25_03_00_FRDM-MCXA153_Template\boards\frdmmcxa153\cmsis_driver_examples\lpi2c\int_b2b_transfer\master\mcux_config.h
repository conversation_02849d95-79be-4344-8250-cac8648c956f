/*
 * Copyright 2025 NXP
 *
 * SPDX-License-Identifier: BSD-3-Clause
 */

#ifndef _MCUX_CONFIG_H_
#define _MCUX_CONFIG_H_

#define CONFIG_FLASH_BASE_ADDRESS 0x0
#define CONFIG_IFLASH_TEST_USE_SECTOR_INDEX_FROM_END 1
#define CONFIG_IFLASH_TEST_USE_ADDRESS 0x0
// #define CONFIG_IFLASH_EXPLICIT_DEVICE_CONFIG_VALUES 0
// #define CONFIG_DBI_USE_MIPI_PANEL 0
// #define CONFIG_STREAM_FLASH 0
#define CONFIG_LV_ATTRIBUTE_MEM_ALIGN 
#define CONFIG_LV_ATTRIBUTE_LARGE_CONST 
// #define CONFIG_BOOT_CUSTOM_DEVICE_SETUP 0

#endif /* _MCUX_CONFIG_H_ */
